package req

// ShareReplyReq 回应分享请求
// 对应 Java: ShareReplyVo
type ShareReplyReq struct {
	// 邀请者用户ID
	InviterId string `json:"inviterId" binding:"required" bindError:"inviterId"`
	// 被分享的目标ID
	TargetId string `json:"targetId" binding:"required" bindError:"targetId"`
	// 回应类型 1-同意，2-拒绝
	DealType int `json:"dealType" binding:"required" bindError:"dealType"`
	// 共享类型 0-设备共享，1-家庭共享
	Type int `json:"type" bindError:"type"`
	// 家庭ID（共享设备时必填）
	FamilyId string `json:"familyId"`
	// 家庭名称（共享家庭时必填）
	FamilyName string `json:"familyName"`
}
