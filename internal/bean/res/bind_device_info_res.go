package res

import "piceacorp.com/device-service/pkg/web/common/time"

// BindDeviceInfoRes 绑定设备信息响应
type BindDeviceInfoRes struct {
	// 设备列表
	DeviceList []*BindDeviceInfoVo `json:"deviceList"`
}

// BindDeviceInfoVo 绑定设备信息VO
type BindDeviceInfoVo struct {
	// 创建者ID (用户ID)
	UserId string `json:"userId"`
	// 设备ID
	DeviceId string `json:"deviceId"`
	// 设备拥有者
	Owner string `json:"owner"`
	// 设备SN
	Sn string `json:"sn"`
	// 创建时间
	CreateTime time.Ptime `json:"createTime"`
	// 设备MAC地址
	Mac string `json:"mac"`
	// 设备昵称
	Nickname string `json:"nickname"`
	// 设备版本
	Versions string `json:"versions"`
	// 最近在线时间
	OnlineTime time.Ptime `json:"onlineTime"`
	// 最近离线时间
	OfflineTime *time.Ptime `json:"offlineTime"`
	// 产品ID
	ProductId string `json:"productId"`
	// 产品图片URL
	PhotoUrl string `json:"photoUrl"`
	// 产品型号代码
	ProductModeCode string `json:"productModeCode"`
	// 设备虚拟ID
	IotId string `json:"iotId"`
	// 在线状态 (由 CommonService 补充)
	OnlineStatus *bool `json:"onlineStatus"`
	// 设备属性 (由 CommonService 补充)
	Properties interface{} `json:"properties"`
	// 设备状态 (0-离线, 1-在线)
	Status *int `json:"status"`
	// 产品名称 (支持多语言)
	ProductName string `json:"productName"`
}
