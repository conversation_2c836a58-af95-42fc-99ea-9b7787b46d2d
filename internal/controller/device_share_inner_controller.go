package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerShareFamily 分享家庭 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceShareInnerController.shareFamily(@RequestBody ShareFamilyVo vo)
// POST /inner/share/family
func InnerShareFamily(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var shareFamilyReq req.ShareFamilyReq
	if err := c.ShouldBindJSON(&shareFamilyReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到分享家庭请求: targetId=%s, familyName=%s, beInviteIds=%v",
		shareFamilyReq.TargetId, shareFamilyReq.FamilyName, shareFamilyReq.BeInviteIds)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return inviteShareService.doShareFamily(vo);
	result := service.DeviceInviteShareService.DoShareFamily(c, &shareFamilyReq)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// InnerRemoveShare 删除分享记录 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceShareInnerController.removeShare(@RequestBody DelShareReq req)
// POST /inner/share/remove
func InnerRemoveShare(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var delShareReq req.DelShareReq
	if err := c.ShouldBindJSON(&delShareReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到删除分享记录请求: inviter=%v, deviceIds=%v",
		delShareReq.Inviter, delShareReq.DeviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return inviteShareService.delShare(req);
	result := service.DeviceInviteShareService.DelShare(c, &delShareReq)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// InnerDelByUserId 根据用户ID删除分享记录 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceShareInnerController.delByUserId(@RequestParam String tenantId, @RequestParam String userId)
// DELETE /inner/share/del/userId
func InnerDelByUserId(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31: @RequestParam默认必传)
	tenantId := c.Query("tenantId")
	userId := c.Query("userId")

	// 2. 参数验证 - 对应 Java: 无显式验证，但参数必传 (遵循规则31: 严格按照java代码校验)
	if tenantId == "" {
		logger.Errorfm(c, "参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	if userId == "" {
		logger.Errorfm(c, "参数验证失败: userId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到根据用户ID删除分享记录请求: tenantId=%s, userId=%s", tenantId, userId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(inviteShareService.delByUserId(tenantId, userId));
	result := service.DeviceInviteShareService.DelByUserId(c, tenantId, userId)
	response := webRes.Cb(result)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, response)
}
