package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// GetKey 获取视频流三元组信息
// 对应 Java: DeviceVideoInfoController.getKey(@RequestParam("sn") String sn)
// GET /app/video/getKey?sn={sn}
func GetKey(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam("sn") String sn (默认必传)
	sn, exists := c.GetQuery("sn")

	// 2. 参数必传检查 - 对应 Java: @RequestParam 默认必传
	if !exists {
		logger.Errorfm(c, "参数验证失败: sn参数未传递")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn不能为空")
		c.<PERSON>(http.StatusOK, result)
		return
	}

	// 3. 参数空值检查 - 对应 Java: if (StringUtils.isBlank(sn))
	if sn == "" {
		logger.Errorfm(c, "参数验证失败: sn为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn不能为空")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到获取视频流三元组请求: sn=%s", sn)

	// 4. 调用服务层处理业务逻辑 - 对应 Java: return deviceLvInfoService.getKey(sn);
	result := service.DeviceVideoInfoService.GetKey(c, sn)

	// 5. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}
