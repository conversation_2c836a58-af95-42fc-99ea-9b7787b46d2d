package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
)

// ActivateDevice 激活设备，获取访问授权令牌
// 对应 Java: RtcDeviceController.activateDevice
// POST /device/rtc/activateDevice
func ActivateDevice(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var activateReq req.RtcDeviceNodeInfoReq
	if err := c.ShouldBindJSON(&activateReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到RTC设备激活请求: sn=%s, mac=%s", activateReq.Sn, activateReq.Mac)

	// 2. 调用服务层处理业务逻辑
	result := service.RtcDeviceTokenInfoService.ActivateDevice(c, &activateReq)

	// 3. 返回响应 - 永远使用 c.JSO<PERSON>(http.StatusOK, response)
	c.<PERSON>(http.StatusOK, result)
}
