package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/dto"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
)

// InnerPropertyGetHandler MQTT属性获取处理器 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: MqttTopicController.propertyGetHandler(@RequestBody MqttPropertyGetReqDTO param)
// POST /inner/mqtt/propertyGetHandler
func InnerPropertyGetHandler(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var param dto.MqttPropertyGetReqDTO
	if err := c.ShouldBindJSON(&param); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到MQTT属性获取处理请求: clientId=%s, topic=%s", param.ClientId, param.Topic)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: deviceBindUserService.propertyGetTopicHandler(param); return ResponseMessage.buildSuccess();
	service.DeviceBindUserService.PropertyGetTopicHandler(c, &param)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cbnd() // 对应 Java: ResponseMessage.buildSuccess()
	c.JSON(http.StatusOK, result)
}
