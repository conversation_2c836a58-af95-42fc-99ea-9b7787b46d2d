package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// BaseBindList 查询基站绑定列表
// 对应 Java: BaseController.baseBindList(@RequestParam String baseId)
// GET /base/baseBindList
func BaseBindList(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam String baseId (遵循规则31: @RequestParam默认必传)
	baseId := c.Query("baseId")
	if baseId == "" {
		logger.Errorfm(c, "参数验证失败: baseId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "baseId")
		c.<PERSON><PERSON><PERSON>(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到查询基站绑定列表请求: baseId=%s", baseId)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return baseBindService.baseBind(baseId);
	result := service.BaseBindService.BaseBind(c, baseId)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// BaseUnAuth 基站解除认证
// 对应 Java: BaseController.baseUnAuth(@RequestBody BaseUnAuthDeviceReq baseUnAuthDeviceReq)
// POST /base/baseUnAuth
func BaseUnAuth(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var baseUnAuthDeviceReq req.BaseUnAuthDeviceReq
	if err := c.ShouldBindJSON(&baseUnAuthDeviceReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到基站解除认证请求: sn=%s", baseUnAuthDeviceReq.Sn)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return baseAuthService.baseUnAuth(baseUnAuthDeviceReq);
	result := service.BaseAuthService.BaseUnAuth(c, &baseUnAuthDeviceReq)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}
