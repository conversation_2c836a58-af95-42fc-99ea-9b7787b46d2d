package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/dto"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// BatchUnBind 批量解绑设备
// 对应 Java: OpenApiController.batchUnBind(@RequestBody UnBindDeviceVo unBindDeviceVo)
// POST /open/device/batchUnBind
func BatchUnBind(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var unBindDeviceVo vo.UnBindDeviceVo
	if err := c.ShouldBindJSON(&unBindDeviceVo); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到批量解绑设备请求: userId=%s, snList=%v",
		unBindDeviceVo.UserId, unBindDeviceVo.SnList)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return openDeviceBindService.batchUnBindDevice(unBindDeviceVo);
	result := service.OpenDeviceBindService.BatchUnBindDevice(c, &unBindDeviceVo)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// BdSkill 百度技能处理
// 对应 Java: OpenApiController.bdSkill(@RequestBody String jsonStr, @RequestHeader("authorization") String authorization, @RequestHeader("accessKey") String accessKey, @RequestHeader("timestamp") String timestamp)
// POST /open/bd/skill
func BdSkill(c *gin.Context) {
	// 1. 获取请求头参数 - 对应 Java: @RequestHeader (遵循规则31: @RequestParam默认必传)
	authorization := c.GetHeader("authorization")
	accessKey := c.GetHeader("accessKey")
	timestamp := c.GetHeader("timestamp")

	// 2. 获取请求体 - 对应 Java: @RequestBody String jsonStr
	jsonStr, exists := c.Get("rawBody")
	if !exists {
		// 如果没有原始body，尝试读取
		body, err := c.GetRawData()
		if err != nil {
			logger.Errorfm(c, "读取请求体失败: error=%v", err)
			result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "请求体不能为空")
			c.JSON(http.StatusOK, result)
			return
		}
		jsonStr = string(body)
	}

	logger.Infof(c, "收到百度技能处理请求: authorization=%s, accessKey=%s, timestamp=%s",
		authorization, accessKey, timestamp)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return bdService.skill(jsonStr, authorization, accessKey, timestamp);
	result := service.BdService.Skill(c, jsonStr.(string), authorization, accessKey, timestamp)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// LargeModelGetReply 获取大模型回复
// 对应 Java: OpenApiController.largeModelGetReply(@RequestBody LargeModelParam param)
// POST /open/largemodel/getReply
func LargeModelGetReply(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var param dto.LargeModelParam
	if err := c.ShouldBindJSON(&param); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到获取大模型回复请求: ak=%s, nluInfos=%v", param.Ak, param.NluInfos)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return bdService.getReply(param);
	result := service.BdService.GetReply(c, &param)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}
