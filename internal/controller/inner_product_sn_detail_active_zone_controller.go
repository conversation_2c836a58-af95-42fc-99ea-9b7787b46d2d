package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerSaveActiveSn 保存一条已激活的sn信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.saveActiveSn(@RequestBody ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity)
// POST /inner/sn/active/insert
func InnerSaveActiveSn(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var entity domain.ProductSnDetailActiveZone
	if err := c.ShouldBindJSON(&entity); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到保存激活SN信息请求: sn=%s, id=%s", entity.Sn, entity.Id)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return productSnDetailActiveZoneService.insertOne(productSnDetailActiveZoneEntity);
	result := service.ProductSnDetailActiveZoneService.InsertOne(c, &entity)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// InnerDelBySnSingle 通过sn删除一条已激活的sn信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.delBySn(@RequestParam String sn)
// DELETE /inner/sn/active/sn
func InnerDelBySnSingle(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam String sn (遵循规则31: @RequestParam默认必传)
	sn := c.Query("sn")
	if sn == "" {
		logger.Errorfm(c, "参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到删除激活SN信息请求: sn=%s", sn)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return productSnDetailActiveZoneService.delBySn(sn);
	result := service.ProductSnDetailActiveZoneService.DelBySn(c, sn)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// InnerDelBySnList 通过sn列表删除已激活的sn信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.delBySn(@RequestBody List<String> sns)
// DELETE /inner/sn/active/sns
func InnerDelBySnList(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var sns []string
	if err := c.ShouldBindJSON(&sns); err != nil {
		c.Error(err)
		return
	}

	logger.Infof(c, "收到批量删除激活SN信息请求: sns=%v", sns)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return productSnDetailActiveZoneService.delBySns(sns);
	result := service.ProductSnDetailActiveZoneService.DelBySns(c, sns)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// InnerCountByGroupId 通过groupId获取sn数量 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.countByGroupId(@RequestParam String groupId)
// GET /inner/sn/active/countByGroupId
func InnerCountByGroupId(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam String groupId (遵循规则31: @RequestParam默认必传)
	groupId := c.Query("groupId")
	if groupId == "" {
		logger.Errorfm(c, "参数验证失败: groupId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "groupId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到根据组ID统计数量请求: groupId=%s", groupId)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.countByGroupId(groupId));
	count, err := service.ProductSnDetailActiveZoneService.CountByGroupId(c, groupId)
	if err != nil {
		logger.Errorfm(c, "根据组ID统计数量失败: groupId=%s, error=%v", groupId, err)
		result := webRes.Ce(webErr.INNER_SERVER_ERROR, err.Error())
		c.JSON(http.StatusOK, result)
		return
	}

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cb(count)
	c.JSON(http.StatusOK, result)
}

// InnerGetBySn 根据sn查询激活信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.getBySn(@RequestParam("sn") String sn)
// GET /inner/sn/active/getBySn
func InnerGetBySn(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam("sn") String sn (遵循规则31: @RequestParam默认必传)
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn"); }
	if sn == "" {
		logger.Errorfm(c, "参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到根据SN查询激活信息请求: sn=%s", sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.getBySn(sn));
	entity, err := service.ProductSnDetailActiveZoneService.GetBySn(c, sn)
	if err != nil {
		logger.Errorfm(c, "根据SN查询激活信息失败: sn=%s, error=%v", sn, err)
		result := webRes.Ce(webErr.INNER_SERVER_ERROR, err.Error())
		c.JSON(http.StatusOK, result)
		return
	}

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cb(entity)
	c.JSON(http.StatusOK, result)
}

// InnerSnCodes 根据产品型号id模糊查询sn (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.snCodes(@RequestParam("productModeId") String productModeId, @RequestParam("sn") String sn)
// GET /inner/sn/active/snCodes
func InnerSnCodes(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31: @RequestParam默认必传)
	productModeId := c.Query("productModeId")
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn"); }
	if sn == "" {
		logger.Errorfm(c, "参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(productModeId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "productModeId"); }
	if productModeId == "" {
		logger.Errorfm(c, "参数验证失败: productModeId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "productModeId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到模糊查询SN请求: productModeId=%s, sn=%s", productModeId, sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.snCodes(productModeId,sn));
	entities, err := service.ProductSnDetailActiveZoneService.SnCodes(c, productModeId, sn)
	if err != nil {
		logger.Errorfm(c, "模糊查询SN失败: productModeId=%s, sn=%s, error=%v", productModeId, sn, err)
		result := webRes.Ce(webErr.INNER_SERVER_ERROR, err.Error())
		c.JSON(http.StatusOK, result)
		return
	}

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cb(entities)
	c.JSON(http.StatusOK, result)
}

// InnerGetBySnList 批量获取已激活的sn (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: InnerProductSnDetailActiveZoneController.getBySnList(List<String> snList)
// GET /inner/sn/active/getBySnList
func InnerGetBySnList(c *gin.Context) {
	// 1. 绑定请求参数 - 注意：Java中这是GET请求但接收List参数，Go中需要特殊处理
	var snList []string
	if err := c.ShouldBindJSON(&snList); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtils.isEmpty(snList)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "snList"); }
	if len(snList) == 0 {
		logger.Errorfm(c, "参数验证失败: snList不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "snList")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof(c, "收到批量查询激活SN请求: snList=%v", snList)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.getBySnList(snList));
	entities, err := service.ProductSnDetailActiveZoneService.GetBySnList(c, snList)
	if err != nil {
		logger.Errorfm(c, "批量查询激活SN失败: snList=%v, error=%v", snList, err)
		result := webRes.Ce(webErr.INNER_SERVER_ERROR, err.Error())
		c.JSON(http.StatusOK, result)
		return
	}

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cb(entities)
	c.JSON(http.StatusOK, result)
}
