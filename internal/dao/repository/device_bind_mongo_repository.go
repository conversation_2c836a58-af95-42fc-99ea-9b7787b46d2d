package repository

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"go.mongodb.org/mongo-driver/v2/bson"
	"piceacorp.com/device-service/pkg/data/mongo"
)

// IDeviceBindMongoRepository 设备绑定MongoDB仓库接口
// 对应 Java: DeviceBindRepository (遵循规则33: java中的MongoTemplate 类比于golang的mongo.DB)
type IDeviceBindMongoRepository interface {
	// 删除绑定关系 - 对应 Java: remove(String userId, List<String> snList)
	Remove(ctx context.Context, userId string, snList []string) (int64, error)
}

// deviceBindMongoRepository 设备绑定MongoDB仓库实现
type deviceBindMongoRepository struct{}

// DeviceBindMongoRepository 全局设备绑定MongoDB仓库实例 (遵循规则15: 只对外暴露全局变量)
var DeviceBindMongoRepository = &deviceBindMongoRepository{}

// Remove 删除绑定关系
// 对应 Java: DeviceBindRepository.remove(String userId, List<String> snList) (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceBindMongoRepository) Remove(ctx context.Context, userId string, snList []string) (int64, error) {
	logger.Infof(ctx, "MongoDB删除绑定关系: userId=%s, snList=%v", userId, snList)

	// 对应 Java: MongoDB删除操作
	collection := mongo.DB.Collection("device_bind")
	filter := bson.M{
		"userId": userId,
		"sn":     bson.M{"$in": snList},
	}

	result, err := collection.DeleteMany(ctx, filter)
	if err != nil {
		logger.Errorfm(ctx, "MongoDB删除绑定关系失败: userId=%s, snList=%v, error=%v", userId, snList, err)
		return 0, err
	}

	logger.Infof(ctx, "MongoDB删除绑定关系成功: userId=%s, snList=%v, deletedCount=%d", userId, snList, result.DeletedCount)
	return result.DeletedCount, nil
}
