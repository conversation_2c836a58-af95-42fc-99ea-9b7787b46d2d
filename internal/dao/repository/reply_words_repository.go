package repository

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
)

// IReplyWordsRepository 回复词仓库接口
// 对应 Java: ReplyWordsMapper (遵循规则45: 严格按照规则6转换Mapper到Repository)
type IReplyWordsRepository interface {
	base.IRepository[domain.ReplyWords]
	// 根据领域和意图查询回复词 - 对应 Java: selectList(wrapper)
	FindByDomainAndIntent(ctx context.Context, domain, intent, condition string) ([]*domain.ReplyWords, error)
	// 根据领域、意图和槽位查询notfound回复词 - 对应 Java: 处理不存在位置的情况
	FindByDomainAndIntentWithNotFound(ctx context.Context, domain, intent, slot string) ([]*domain.ReplyWords, error)
}

// replyWordsRepository 回复词仓库实现 (遵循规则6: 严格按照现有Repository模式转换)
type replyWordsRepository struct {
	base.BaseRepository[domain.ReplyWords]
}

// ReplyWordsRepository 全局回复词仓库实例 (遵循规则15: 只对外暴露全局变量)
var ReplyWordsRepository = &replyWordsRepository{
	BaseRepository: base.BaseRepository[domain.ReplyWords]{Dbh: mysql.DBH},
}

// FindByDomainAndIntent 根据领域和意图查询回复词
// 对应 Java: LambdaQueryWrapper<ReplyWordsEntity> wrapper = new LambdaQueryWrapper<>(); wrapper.eq(ReplyWordsEntity::getDomain, domain).eq(ReplyWordsEntity::getIntention, intent); List<ReplyWordsEntity> replyWords = replyWordsMapper.selectList(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *replyWordsRepository) FindByDomainAndIntent(ctx context.Context, domainStr, intent, condition string) ([]*domain.ReplyWords, error) {
	logger.Infof(ctx, "根据领域和意图查询回复词: domain=%s, intent=%s", domainStr, intent)

	var replyWords []*domain.ReplyWords

	// 对应 Java:
	// LambdaQueryWrapper<ReplyWords> queryWrapper = new LambdaQueryWrapper<>();
	// queryWrapper.eq(ReplyWords::getDomain, domain)
	// .eq(ReplyWords::getHandleFlag, 0)
	// .eq(ReplyWords::getIntention, intent)
	// .ne(ReplyWords::getConditionExpression, NOT_FOUND)
	// .orderByAsc(ReplyWords::getPriority);
	result := r.Dbh.DB.WithContext(ctx).
		Where("domain = ? AND intention = ? AND handle_flag = ? AND condition_expression != ?", domainStr, intent, 0, condition).
		Order("priority asc").
		Find(&replyWords)

	if result.Error != nil {
		logger.Errorfm(ctx, "查询回复词失败: domain=%s, intent=%s, error=%v", domainStr, intent, result.Error)
		return nil, result.Error
	}

	return replyWords, nil
}

// FindByDomainAndIntentWithNotFound 根据领域、意图和槽位查询notfound回复词
// 对应 Java: queryWrapper.eq(ReplyWords::getDomain, domain).eq(ReplyWords::getIntention, intent).eq(ReplyWords::getSlot, slot).eq(ReplyWords::getConditionExpression, NOT_FOUND);
func (r *replyWordsRepository) FindByDomainAndIntentWithNotFound(ctx context.Context, domainStr, intent, slot, condition string) ([]*domain.ReplyWords, error) {
	logger.Infof(ctx, "查询不存在位置回复词: domain=%s, intent=%s, slot=%s", domainStr, intent, slot)

	var replyWords []*domain.ReplyWords

	// 对应 Java: queryWrapper.eq(ReplyWords::getDomain, domain).eq(ReplyWords::getIntention, intent).eq(ReplyWords::getSlot, slot).eq(ReplyWords::getConditionExpression, NOT_FOUND);
	result := r.Dbh.DB.WithContext(ctx).
		Where("domain = ? AND intention = ? AND slot = ? AND condition_expression = ?'", domainStr, intent, slot, condition).
		Find(&replyWords)

	if result.Error != nil {
		logger.Errorfm(ctx, "查询不存在位置回复词失败: domain=%s, intent=%s, slot=%s, error=%v", domainStr, intent, slot, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "查询不存在位置回复词成功: domain=%s, intent=%s, slot=%s, count=%d", domainStr, intent, slot, len(replyWords))
	return replyWords, nil
}
