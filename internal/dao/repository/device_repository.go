package repository

import (
	"context"
	"errors"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"

	"go.3irobotix.net/aiot/common-log/logger"
	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/domain"
)

// DeviceRepository 设备仓库接口
type IDeviceRepository interface {
	base.IRepository[domain.DeviceInfo]
	FindBySN(ctx context.Context, sn string) (*domain.DeviceInfo, error)
	// 新增方法：根据MAC地址查找设备
	FindByMAC(ctx context.Context, mac string) (*domain.DeviceInfo, error)
	// 更新设备昵称 (对应 DeviceInfoMapper.update)
	UpdateNickname(ctx context.Context, deviceId, nickname, defaultNickname string) error
	// 根据SN和MAC获取设备信息 (对应 DeviceInfoServiceImpl.getBySnAndMac)
	GetBySnAndMac(ctx context.Context, sn, mac string) (*domain.DeviceInfo, error)
	// 查询所有设备SN列表 (对应 selectAllRobotSn) (遵循规则43: 必须全部转换)
	SelectAllSn(ctx context.Context) ([]*domain.DeviceInfo, error)
	// 根据产品ID列表统计设备数量 (对应 selectCountByProuctId) (遵循规则43: 必须全部转换)
	SelectCountByProductId(ctx context.Context, productIds []string) (map[string]int64, error)
	// 根据ID列表查询设备信息 (对应 listByIds) (遵循规则43: 必须全部转换)
	FindByIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceInfo, error)
	// 根据ID查询设备信息 (对应 getById) (遵循规则43: 必须全部转换)
	FindById(ctx context.Context, deviceId string) (*domain.DeviceInfo, error)
	// 根据产品型号代码统计设备数量 (对应 getCountByCode) (遵循规则43: 必须全部转换)
	CountByProductModeCode(ctx context.Context, productModeCode string) (int64, error)
	// 更新设备验证密码 (对应 updateVerifyPassword) (遵循规则43: 必须全部转换)
	UpdateVerifyPassword(ctx context.Context, deviceIds []string, verifyPassword int) (int64, error)
	// 根据产品ID更新设备图片 (对应 updatePhoto) (遵循规则43: 必须全部转换)
	UpdatePhotoByProductId(ctx context.Context, productId, tenantId, photoUrl string) (int64, error)
	// 根据产品型号代码和租户ID统计设备数量 (对应 getCountByCode) (遵循规则43: 必须全部转换)
	GetCountByCodeAndTenant(ctx context.Context, productModeCode, tenantId string) (int64, error)
	// 根据产品型号代码获取活跃设备SN (对应 getActiveDeviceSNByCode) (遵循规则43: 必须全部转换)
	GetActiveDeviceSNByCode(ctx context.Context, productModeCode, tenantId string, limit int) ([]string, error)
	// 重置设备昵称 (对应 resetNickname) (遵循规则43: 必须全部转换)
	ResetNickname(ctx context.Context, sn, tenantId, defaultNickname string) (int64, error)
	// 根据产品型号代码分页获取设备 (对应 getPageByCode) (遵循规则47: 必须全部转换)
	GetPageByProductModeCode(ctx context.Context, productModeCode string, page, pageSize int) (*bo.PageResult[*domain.DeviceInfo], error)
	// 根据设备ID列表查询设备 (对应 getSnListByDeviceIds) (遵循规则47: 必须全部转换)
	GetByDeviceIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceInfo, error)
	// 通过id更新设备信息 - 对应 Java: updateById(DeviceInfoEntity entity) (遵循规则47: 必须全部转换)
	UpdateById(ctx context.Context, entity *domain.DeviceInfo) error
	// 通过设备id更新iotId - 对应 Java: updateIotId(String deviceId, String iotId) (遵循规则47: 必须全部转换)
	UpdateIotId(ctx context.Context, deviceId, iotId string) (int64, error)
}

// deviceRepository 设备仓库实现
type deviceRepository struct {
	base.BaseRepository[domain.DeviceInfo]
}

var DeviceRepository = &deviceRepository{BaseRepository: base.BaseRepository[domain.DeviceInfo]{Dbh: mysql.DBH}}

// FindBySN 根据SN查找设备
func (r *deviceRepository) FindBySN(ctx context.Context, sn string) (*domain.DeviceInfo, error) {
	var device domain.DeviceInfo
	result := r.Dbh.DB.WithContext(ctx).Where("sn = ?", sn).First(&device)
	if result.Error != nil {
		logger.Error(ctx, "查询设备失败", result.Error)
		return nil, nil
	}
	return &device, nil
}

// FindByMAC 根据MAC地址查找设备
func (r *deviceRepository) FindByMAC(ctx context.Context, mac string) (*domain.DeviceInfo, error) {
	var device domain.DeviceInfo
	mysql.IgnoreTenant()
	result := r.Dbh.DB.WithContext(ctx).Where("mac = ?", mac).First(&device)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Error(ctx, "根据MAC查询设备失败", result.Error)
		return nil, result.Error
	}
	return &device, nil
}

// UpdateNickname 更新设备昵称
// 对应 Java: deviceInfoMapper.update(entity, wrapper)
func (r *deviceRepository) UpdateNickname(ctx context.Context, sn, deviceId, tenantId, nickname, defaultNickname string) error {
	// 准备更新字段
	updates := make(map[string]interface{})
	if nickname != "" {
		updates["nickname"] = nickname
	}
	if defaultNickname != "" {
		updates["default_nickname"] = defaultNickname
	}

	// 如果没有要更新的字段，直接返回
	if len(updates) == 0 {
		return nil
	}

	// 构建查询条件
	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceInfo{})

	// 只有不为空时才添加条件
	if sn != "" {
		query = query.Where("sn = ?", sn)
	}
	if deviceId != "" {
		query = query.Where("id = ?", deviceId)
	}
	if tenantId != "" {
		query = query.Where("tenant_id = ?", tenantId)
	}

	// 执行更新
	result := query.Updates(updates)

	if result.Error != nil {
		logger.Errorfm(ctx, "更新设备昵称失败: sn=%s, deviceId=%s, error=%v", sn, deviceId, result.Error)
		return result.Error
	}

	logger.Infof(ctx, "更新设备昵称成功: sn=%s, deviceId=%s, affected=%d", sn, deviceId, result.RowsAffected)
	return nil
}

// GetBySnAndMac 根据SN和MAC获取设备信息
// 对应 Java: DeviceInfoServiceImpl.getBySnAndMac(String sn, String mac)
// SQL: SELECT * FROM t_device_info WHERE sn = ? AND mac = ?
func (r *deviceRepository) GetBySnAndMac(ctx context.Context, sn, mac string) (*domain.DeviceInfo, error) {
	var device domain.DeviceInfo

	result := r.Dbh.DB.WithContext(ctx).
		Where("sn = ? AND mac = ?", sn, mac).
		First(&device)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			logger.Debugf(ctx, "设备不存在: sn=%s, mac=%s", sn, mac)
			return nil, nil
		}
		logger.Errorfm(ctx, "根据SN和MAC查询设备失败: sn=%s, mac=%s, error=%v", sn, mac, result.Error)
		return nil, result.Error
	}

	logger.Debugf(ctx, "根据SN和MAC查询设备成功: sn=%s, mac=%s, deviceId=%s", sn, mac, device.Id)
	return &device, nil
}

// SelectAllSn 查询所有设备SN列表
// 对应 Java: QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>(); wrapper.select("sn"); List<DeviceInfoEntity> deviceInfoEntities = deviceInfoMapper.selectList(wrapper); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) SelectAllSn(ctx context.Context) ([]*domain.DeviceInfo, error) {
	var devices []*domain.DeviceInfo

	// 对应 Java: wrapper.select("sn")
	result := r.Dbh.DB.WithContext(ctx).Select("sn").Find(&devices)
	if result.Error != nil {
		logger.Errorfm(ctx, "查询所有设备SN列表失败: error=%v", result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "查询所有设备SN列表成功: count=%d", len(devices))
	return devices, nil
}

// SelectCountByProductId 根据产品ID列表统计设备数量
// 对应 Java: List<Map<String, Long>> res = baseMapper.selectCountByProuctId(productIds); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) SelectCountByProductId(ctx context.Context, productIds []string) (map[string]int64, error) {
	logger.Infof(ctx, "根据产品ID列表统计设备数量: productIds=%v", productIds)

	// 对应 Java SQL: SELECT product_id, COUNT(*) as device_num FROM t_device_info WHERE product_id IN (...) GROUP BY product_id
	var results []struct {
		ProductId string `gorm:"column:product_id"`
		DeviceNum int64  `gorm:"column:device_num"`
	}

	err := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Select("product_id, COUNT(*) as device_num").
		Where("product_id IN ?", productIds).
		Group("product_id").
		Scan(&results).Error

	if err != nil {
		logger.Errorfm(ctx, "根据产品ID列表统计设备数量失败: productIds=%v, error=%v", productIds, err)
		return nil, err
	}

	// 对应 Java: Map<String, Long> map = new HashMap<>(); for (Map<String, Long> resMap : res) { map.put(String.valueOf(resMap.get("product_id")), Long.valueOf(resMap.get("device_num").toString())); }
	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.ProductId] = result.DeviceNum
	}

	logger.Infof(ctx, "根据产品ID列表统计设备数量成功: productIds=%v, result=%v", productIds, countMap)
	return countMap, nil
}

// FindByIds 根据ID列表查询设备信息
// 对应 Java: this.listByIds(ids) (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) FindByIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceInfo, error) {
	var devices []*domain.DeviceInfo

	result := r.Dbh.DB.WithContext(ctx).Where("id IN ?", deviceIds).Find(&devices)
	if result.Error != nil {
		logger.Errorfm(ctx, "根据ID列表查询设备信息失败: deviceIds=%v, error=%v", deviceIds, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "根据ID列表查询设备信息成功: deviceIds=%v, count=%d", deviceIds, len(devices))
	return devices, nil
}

// FindById 根据ID查询设备信息
// 对应 Java: this.getById(deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) FindById(ctx context.Context, deviceId string) (*domain.DeviceInfo, error) {
	var device domain.DeviceInfo

	result := r.Dbh.DB.WithContext(ctx).Where("id = ?", deviceId).First(&device)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Errorfm(ctx, "根据ID查询设备信息失败: deviceId=%s, error=%v", deviceId, result.Error)
		return nil, result.Error
	}

	logger.Debugf(ctx, "根据ID查询设备信息成功: deviceId=%s", deviceId)
	return &device, nil
}

// CountByProductModeCode 根据产品型号代码统计设备数量
// 对应 Java: QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(DeviceInfoEntity::getProductModeCode, productModeCode); return deviceInfoMapper.selectCount(wrapper).intValue(); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) CountByProductModeCode(ctx context.Context, productModeCode string) (int64, error) {
	var count int64

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("product_mode_code = ?", productModeCode).
		Count(&count)

	if result.Error != nil {
		logger.Errorfm(ctx, "根据产品型号代码统计设备数量失败: productModeCode=%s, error=%v", productModeCode, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据产品型号代码统计设备数量成功: productModeCode=%s, count=%d", productModeCode, count)
	return count, nil
}

// UpdateVerifyPassword 更新设备验证密码
// 对应 Java: UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>(); wrapper.lambda().set(DeviceInfoEntity::getVerifyPassword, vo.getVerifyPassword()).in(DeviceInfoEntity::getId, vo.getDeviceIds()); int update = deviceInfoMapper.update(null, wrapper); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) UpdateVerifyPassword(ctx context.Context, deviceIds []string, verifyPassword int) (int64, error) {
	logger.Infof(ctx, "更新设备验证密码: deviceIds=%v, verifyPassword=%d", deviceIds, verifyPassword)

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("id IN ?", deviceIds).
		Update("verify_password", verifyPassword)

	if result.Error != nil {
		logger.Errorfm(ctx, "更新设备验证密码失败: deviceIds=%v, verifyPassword=%d, error=%v", deviceIds, verifyPassword, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "更新设备验证密码成功: deviceIds=%v, verifyPassword=%d, rowsAffected=%d", deviceIds, verifyPassword, result.RowsAffected)
	return result.RowsAffected, nil
}

// UpdatePhotoByProductId 根据产品ID更新设备图片
// 对应 Java: UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>(); wrapper.lambda().set(DeviceInfoEntity::getPhotoUrl, vo.getPhotoUrl()).eq(DeviceInfoEntity::getProductId, vo.getProductId()).eq(DeviceInfoEntity::getTenantId, vo.getTenantId()); int update = deviceInfoMapper.update(null, wrapper); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) UpdatePhotoByProductId(ctx context.Context, productId, tenantId, photoUrl string) (int64, error) {
	logger.Infof(ctx, "根据产品ID更新设备图片: productId=%s, tenantId=%s, photoUrl=%s", productId, tenantId, photoUrl)

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("product_id = ? AND tenant_id = ?", productId, tenantId).
		Update("photo_url", photoUrl)

	if result.Error != nil {
		logger.Errorfm(ctx, "根据产品ID更新设备图片失败: productId=%s, tenantId=%s, photoUrl=%s, error=%v", productId, tenantId, photoUrl, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据产品ID更新设备图片成功: productId=%s, tenantId=%s, photoUrl=%s, rowsAffected=%d", productId, tenantId, photoUrl, result.RowsAffected)
	return result.RowsAffected, nil
}

// GetCountByCodeAndTenant 根据产品型号代码和租户ID统计设备数量
// 对应 Java: Long total = this.baseMapper.getCountByCode(deviceInfoReq.getProductModeCode(), deviceInfoReq.getTenantId()); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) GetCountByCodeAndTenant(ctx context.Context, productModeCode, tenantId string) (int64, error) {
	var count int64

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("product_mode_code = ? AND tenant_id = ?", productModeCode, tenantId).
		Count(&count)

	if result.Error != nil {
		logger.Errorfm(ctx, "根据产品型号代码和租户ID统计设备数量失败: productModeCode=%s, tenantId=%s, error=%v", productModeCode, tenantId, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据产品型号代码和租户ID统计设备数量成功: productModeCode=%s, tenantId=%s, count=%d", productModeCode, tenantId, count)
	return count, nil
}

// GetActiveDeviceSNByCode 根据产品型号代码获取活跃设备SN
// 对应 Java: LambdaQueryWrapper<DeviceInfoEntity> queryWrapper = new LambdaQueryWrapper(); queryWrapper.select(DeviceInfoEntity::getSn); queryWrapper.eq(DeviceInfoEntity::getProductModeCode, deviceInfoReq.getProductModeCode()).eq(DeviceInfoEntity::getTenantId, deviceInfoReq.getTenantId()); queryWrapper.orderByDesc(DeviceInfoEntity::getOnlineTime); queryWrapper.last("limit " + queryCount); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) GetActiveDeviceSNByCode(ctx context.Context, productModeCode, tenantId string, limit int) ([]string, error) {
	var devices []struct {
		Sn string `gorm:"column:sn"`
	}

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Select("sn").
		Where("product_mode_code = ? AND tenant_id = ?", productModeCode, tenantId).
		Order("online_time DESC").
		Limit(limit).
		Scan(&devices)

	if result.Error != nil {
		logger.Errorfm(ctx, "根据产品型号代码获取活跃设备SN失败: productModeCode=%s, tenantId=%s, limit=%d, error=%v", productModeCode, tenantId, limit, result.Error)
		return nil, result.Error
	}

	// 转换为字符串切片
	snList := make([]string, len(devices))
	for i, device := range devices {
		snList[i] = device.Sn
	}

	logger.Infof(ctx, "根据产品型号代码获取活跃设备SN成功: productModeCode=%s, tenantId=%s, limit=%d, count=%d", productModeCode, tenantId, limit, len(snList))
	return snList, nil
}

// ResetNickname 重置设备昵称
// 对应 Java: lambda.eq(DeviceInfoEntity::getSn, sn).eq(DeviceInfoEntity::getTenantId, tenantId).set(DeviceInfoEntity::getNickname, deviceInfo.getDefaultNickname()).set(DeviceInfoEntity::getDefaultNickname, deviceInfo.getDefaultNickname()); int update = deviceInfoMapper.update(null, wrapper); (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceRepository) ResetNickname(ctx context.Context, sn, tenantId, defaultNickname string) (int64, error) {
	logger.Infof(ctx, "重置设备昵称: sn=%s, tenantId=%s, defaultNickname=%s", sn, tenantId, defaultNickname)

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("sn = ? AND tenant_id = ?", sn, tenantId).
		Updates(map[string]interface{}{
			"nickname":         defaultNickname,
			"default_nickname": defaultNickname,
		})

	if result.Error != nil {
		logger.Errorfm(ctx, "重置设备昵称失败: sn=%s, tenantId=%s, defaultNickname=%s, error=%v", sn, tenantId, defaultNickname, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "重置设备昵称成功: sn=%s, tenantId=%s, defaultNickname=%s, rowsAffected=%d", sn, tenantId, defaultNickname, result.RowsAffected)
	return result.RowsAffected, nil
}

// GetPageByProductModeCode 根据产品型号代码分页获取设备
// 对应 Java: this.page(new Page<>(page, pageSize), wrapper) (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceRepository) GetPageByProductModeCode(ctx context.Context, productModeCode string, page, pageSize int) (*bo.PageResult[*domain.DeviceInfo], error) {
	logger.Infof(ctx, "根据产品型号代码分页获取设备: productModeCode=%s, page=%d, pageSize=%d", productModeCode, page, pageSize)

	var devices []*domain.DeviceInfo
	var total int64

	// 对应 Java: wrapper.lambda().eq(DeviceInfoEntity::getProductModeCode, productModeCode)
	db := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceInfo{}).Where("product_mode_code = ?", productModeCode)

	// 查询总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorfm(ctx, "查询设备总数失败: productModeCode=%s, error=%v", productModeCode, err)
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).Limit(pageSize).Find(&devices).Error; err != nil {
		logger.Errorfm(ctx, "分页查询设备失败: productModeCode=%s, page=%d, pageSize=%d, error=%v",
			productModeCode, page, pageSize, err)
		return nil, err
	}

	// 构造分页结果 (遵循规则6: 非domain的返回放到internal/dao/bo文件夹中)
	pageResult := bo.NewPageResult(devices, total, page, pageSize)

	logger.Infof(ctx, "根据产品型号代码分页获取设备成功: productModeCode=%s, page=%d, pageSize=%d, total=%d",
		productModeCode, page, pageSize, total)
	return pageResult, nil
}

// GetByDeviceIds 根据设备ID列表查询设备
// 对应 Java: wrapper.in(DeviceInfoEntity::getId, deviceIds); List<DeviceInfoEntity> deviceList = this.list(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceRepository) GetByDeviceIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceInfo, error) {
	logger.Infof(ctx, "根据设备ID列表查询设备: deviceIds=%v", deviceIds)

	var devices []*domain.DeviceInfo

	// 对应 Java: wrapper.in(DeviceInfoEntity::getId, deviceIds)
	result := r.Dbh.DB.WithContext(ctx).Where("id IN ?", deviceIds).Find(&devices)
	if result.Error != nil {
		logger.Errorfm(ctx, "根据设备ID列表查询设备失败: deviceIds=%v, error=%v", deviceIds, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "根据设备ID列表查询设备成功: deviceIds=%v, count=%d", deviceIds, len(devices))
	return devices, nil
}

// UpdateById 通过id更新设备信息
// 对应 Java: updateById(DeviceInfoEntity entity) (遵循规则47: 严格按照Java编码内容转换)
func (r *deviceRepository) UpdateById(ctx context.Context, entity *domain.DeviceInfo) error {
	logger.Infof(ctx, "通过id更新设备信息: id=%s", entity.Id)

	// 对应 Java: MyBatis-Plus的updateById方法
	result := r.Dbh.DB.WithContext(ctx).Save(entity)
	if result.Error != nil {
		logger.Errorfm(ctx, "通过id更新设备信息失败: id=%s, error=%v", entity.Id, result.Error)
		return result.Error
	}

	logger.Infof(ctx, "通过id更新设备信息成功: id=%s, rowsAffected=%d", entity.Id, result.RowsAffected)
	return nil
}

// UpdateIotId 通过设备id更新iotId
// 对应 Java: @Update("update t_device_info set iot_id=#{iotId} where id=#{deviceId}") int updateIotId(String deviceId, String iotId); (遵循规则47: 严格按照Java编码内容转换)
func (r *deviceRepository) UpdateIotId(ctx context.Context, deviceId, iotId string) (int64, error) {
	logger.Infof(ctx, "通过设备id更新iotId: deviceId=%s, iotId=%s", deviceId, iotId)

	// 对应 Java: update t_device_info set iot_id=#{iotId} where id=#{deviceId}
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInfo{}).
		Where("id = ?", deviceId).
		Update("iot_id", iotId)

	if result.Error != nil {
		logger.Errorfm(ctx, "通过设备id更新iotId失败: deviceId=%s, error=%v", deviceId, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "通过设备id更新iotId成功: deviceId=%s, rowsAffected=%d", deviceId, result.RowsAffected)
	return result.RowsAffected, nil
}
