package repository

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
)

// IProductSnDetailActiveZoneRepository SN激活仓库接口
// 对应 Java: ProductSnDetailActiveZoneMapper (遵循规则45: 严格按照规则6转换Mapper到Repository)
type IProductSnDetailActiveZoneRepository interface {
	base.IRepository[domain.ProductSnDetailActiveZone]
	// 根据SN查询激活信息 - 对应 Java: selectOne(wrapper)
	GetBySn(ctx context.Context, sn string) (*domain.ProductSnDetailActiveZone, error)
	// 插入激活信息 - 对应 Java: insert(productSnDetailActiveZoneEntity)
	Insert(ctx context.Context, entity *domain.ProductSnDetailActiveZone) error
	// 根据SN删除激活信息 - 对应 Java: delete(wrapper)
	DeleteBySn(ctx context.Context, sn string) (int64, error)
	// 根据SN列表删除激活信息 - 对应 Java: delete(wrapper.in(...))
	DeleteBySns(ctx context.Context, sns []string) (int64, error)
	// 根据组ID统计数量 - 对应 Java: count(activeWrapper)
	CountByGroupId(ctx context.Context, groupId string) (int64, error)
	// 模糊查询SN - 对应 Java: snCodes(productModeId, sn)
	SnCodes(ctx context.Context, productModeId, sn string) ([]*domain.ProductSnDetailActiveZone, error)
	// 根据SN列表查询 - 对应 Java: list(lambda.in(...))
	GetBySnList(ctx context.Context, snList []string) ([]*domain.ProductSnDetailActiveZone, error)
}

// productSnDetailActiveZoneRepository SN激活仓库实现 (遵循规则6: 严格按照现有Repository模式转换)
type productSnDetailActiveZoneRepository struct {
	base.BaseRepository[domain.ProductSnDetailActiveZone]
}

// ProductSnDetailActiveZoneRepository 全局SN激活仓库实例 (遵循规则15: 只对外暴露全局变量)
var ProductSnDetailActiveZoneRepository = &productSnDetailActiveZoneRepository{
	BaseRepository: base.BaseRepository[domain.ProductSnDetailActiveZone]{Dbh: mysql.DBH},
}

// GetBySn 根据SN查询激活信息
// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn); return productSnDetailActiveZoneMapper.selectOne(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) GetBySn(ctx context.Context, sn string) (*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "根据SN查询激活信息: sn=%s", sn)

	var entity domain.ProductSnDetailActiveZone

	// 对应 Java: wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn)
	result := r.Dbh.DB.WithContext(ctx).Where("sn = ?", sn).First(&entity)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			logger.Infof(ctx, "根据SN查询激活信息未找到: sn=%s", sn)
			return nil, nil
		}
		logger.Errorfm(ctx, "根据SN查询激活信息失败: sn=%s, error=%v", sn, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "根据SN查询激活信息成功: sn=%s, id=%s", sn, entity.Id)
	return &entity, nil
}

// Insert 插入激活信息
// 对应 Java: int insert = productSnDetailActiveZoneMapper.insert(productSnDetailActiveZoneEntity); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) Insert(ctx context.Context, entity *domain.ProductSnDetailActiveZone) error {
	logger.Infof(ctx, "插入激活信息: sn=%s, id=%s", entity.Sn, entity.Id)

	// 对应 Java: productSnDetailActiveZoneMapper.insert(productSnDetailActiveZoneEntity)
	result := r.Dbh.DB.WithContext(ctx).Create(entity)
	if result.Error != nil {
		logger.Errorfm(ctx, "插入激活信息失败: sn=%s, id=%s, error=%v", entity.Sn, entity.Id, result.Error)
		return result.Error
	}

	logger.Infof(ctx, "插入激活信息成功: sn=%s, id=%s", entity.Sn, entity.Id)
	return nil
}

// DeleteBySn 根据SN删除激活信息
// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn); int delete = productSnDetailActiveZoneMapper.delete(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) DeleteBySn(ctx context.Context, sn string) (int64, error) {
	logger.Infof(ctx, "根据SN删除激活信息: sn=%s", sn)

	// 对应 Java: wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn)
	result := r.Dbh.DB.WithContext(ctx).Where("sn = ?", sn).Delete(&domain.ProductSnDetailActiveZone{})
	if result.Error != nil {
		logger.Errorfm(ctx, "根据SN删除激活信息失败: sn=%s, error=%v", sn, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据SN删除激活信息成功: sn=%s, rowsAffected=%d", sn, result.RowsAffected)
	return result.RowsAffected, nil
}

// DeleteBySns 根据SN列表删除激活信息
// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().in(ProductSnDetailActiveZoneEntity::getSn, sns); int delete = productSnDetailActiveZoneMapper.delete(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) DeleteBySns(ctx context.Context, sns []string) (int64, error) {
	logger.Infof(ctx, "根据SN列表删除激活信息: sns=%v", sns)

	// 对应 Java: wrapper.lambda().in(ProductSnDetailActiveZoneEntity::getSn, sns)
	result := r.Dbh.DB.WithContext(ctx).Where("sn IN ?", sns).Delete(&domain.ProductSnDetailActiveZone{})
	if result.Error != nil {
		logger.Errorfm(ctx, "根据SN列表删除激活信息失败: sns=%v, error=%v", sns, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据SN列表删除激活信息成功: sns=%v, rowsAffected=%d", sns, result.RowsAffected)
	return result.RowsAffected, nil
}

// CountByGroupId 根据组ID统计数量
// 对应 Java: LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> activeWrapper = new LambdaQueryWrapper<>(); activeWrapper.eq(ProductSnDetailActiveZoneEntity::getGroupId, groupId); return this.count(activeWrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) CountByGroupId(ctx context.Context, groupId string) (int64, error) {
	logger.Infof(ctx, "根据组ID统计数量: groupId=%s", groupId)

	var count int64

	// 对应 Java: activeWrapper.eq(ProductSnDetailActiveZoneEntity::getGroupId, groupId)
	result := r.Dbh.DB.WithContext(ctx).Model(&domain.ProductSnDetailActiveZone{}).Where("group_id = ?", groupId).Count(&count)
	if result.Error != nil {
		logger.Errorfm(ctx, "根据组ID统计数量失败: groupId=%s, error=%v", groupId, result.Error)
		return 0, result.Error
	}

	logger.Infof(ctx, "根据组ID统计数量成功: groupId=%s, count=%d", groupId, count)
	return count, nil
}

// SnCodes 模糊查询SN
// 对应 Java: @Select("select * from t_product_sn_detail_active_zone where product_mode_id=#{productModeId} and sn like concat('%',#{sn},'%') order by create_time desc") List<ProductSnDetailActiveZoneEntity> snCodes(@Param("productModeId") String productModeId, @Param("sn") String sn); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) SnCodes(ctx context.Context, productModeId, sn string) ([]*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "模糊查询SN: productModeId=%s, sn=%s", productModeId, sn)

	var entities []*domain.ProductSnDetailActiveZone

	// 对应 Java: where product_mode_id=#{productModeId} and sn like concat('%',#{sn},'%') order by create_time desc
	result := r.Dbh.DB.WithContext(ctx).
		Where("product_mode_id = ? AND sn LIKE ?", productModeId, "%"+sn+"%").
		Order("create_time DESC").
		Find(&entities)

	if result.Error != nil {
		logger.Errorfm(ctx, "模糊查询SN失败: productModeId=%s, sn=%s, error=%v", productModeId, sn, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "模糊查询SN成功: productModeId=%s, sn=%s, count=%d", productModeId, sn, len(entities))
	return entities, nil
}

// GetBySnList 根据SN列表查询
// 对应 Java: LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> lambda = new QueryWrapper<ProductSnDetailActiveZoneEntity>().lambda(); lambda.in(ProductSnDetailActiveZoneEntity::getSn, snList); return this.list(lambda); (遵循规则46: 严格按照Java编码内容转换)
func (r *productSnDetailActiveZoneRepository) GetBySnList(ctx context.Context, snList []string) ([]*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "根据SN列表查询: snList=%v", snList)

	var entities []*domain.ProductSnDetailActiveZone

	// 对应 Java: lambda.in(ProductSnDetailActiveZoneEntity::getSn, snList)
	result := r.Dbh.DB.WithContext(ctx).Where("sn IN ?", snList).Find(&entities)
	if result.Error != nil {
		logger.Errorfm(ctx, "根据SN列表查询失败: snList=%v, error=%v", snList, result.Error)
		return nil, result.Error
	}

	logger.Infof(ctx, "根据SN列表查询成功: snList=%v, count=%d", snList, len(entities))
	return entities, nil
}
