package repository

import (
	"context"
	"errors"

	"go.3irobotix.net/aiot/common-log/logger"
	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
)

// IDeviceVideoInfoRepository 设备视频信息仓库接口
type IDeviceVideoInfoRepository interface {
	base.IRepository[domain.DeviceVideoInfo]
	// 根据SN获取设备视频信息
	GetBySn(ctx context.Context, sn string) (*domain.DeviceVideoInfo, error)
}

// deviceVideoInfoRepository 设备视频信息仓库实现
type deviceVideoInfoRepository struct {
	base.BaseRepository[domain.DeviceVideoInfo]
}

var DeviceVideoInfoRepository = &deviceVideoInfoRepository{BaseRepository: base.BaseRepository[domain.DeviceVideoInfo]{Dbh: mysql.DBH}}

// GetBySn 根据SN获取设备视频信息
// 对应 Java: DeviceVideoInfoServiceImpl.getKey() 中的 baseMapper.selectOne(queryWrapper)
// SQL: SELECT * FROM t_device_video_info WHERE sn = ? LIMIT 1
func (r *deviceVideoInfoRepository) GetBySn(ctx context.Context, sn string) (*domain.DeviceVideoInfo, error) {
	var deviceVideoInfo domain.DeviceVideoInfo

	result := r.Dbh.DB.WithContext(ctx).
		Where("sn = ?", sn).
		First(&deviceVideoInfo)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			logger.Debugf(ctx, "设备视频信息不存在: sn=%s", sn)
			return nil, nil
		}
		logger.Errorfm(ctx, "根据SN查询设备视频信息失败: sn=%s, error=%v", sn, result.Error)
		return nil, result.Error
	}

	logger.Debugf(ctx, "根据SN查询设备视频信息成功: sn=%s, deviceName=%s", sn, deviceVideoInfo.DeviceName)
	return &deviceVideoInfo, nil
}
