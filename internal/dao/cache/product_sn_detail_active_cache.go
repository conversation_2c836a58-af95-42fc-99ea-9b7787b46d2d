package cache

import (
	"context"
	"fmt"

	"go.3irobotix.net/aiot/common-log/logger"
	redis "piceacorp.com/device-service/pkg/data/cache"
)

// IProductSnDetailActiveCache SN激活缓存接口
// 对应 Java: ProductSnDetailActiveCache (遵循规则7: 放到internal/dao/cache文件夹下)
type IProductSnDetailActiveCache interface {
	// 批量删除SN缓存 - 对应 Java: removeCacheBySns(List<String> sns)
	RemoveCacheBySns(ctx context.Context, sns []string) error
	// 删除指定key的缓存 - 对应 Java: removeKey(String key)
	RemoveKey(ctx context.Context, key string) error
	// 获取SN缓存key列表 - 对应 Java: keys(List<String> sns)
	GetKeys(sns []string) []string
}

var ProductSnDetailActiveCache productSnDetailActiveCache

type productSnDetailActiveCache struct{}

// RemoveCacheBySns 批量删除SN缓存
// 对应 Java: ProductSnDetailActiveCache.removeCacheBySns(List<String> sns) (遵循规则46: 严格按照Java编码内容转换)
func (c *productSnDetailActiveCache) RemoveCacheBySns(ctx context.Context, sns []string) error {
	// 对应 Java: if(CollectionUtils.isEmpty(sns)){ return; }
	if len(sns) == 0 {
		logger.Debugf(ctx, "SN列表为空，无需删除缓存")
		return nil
	}

	// 对应 Java: List<String> keys = keys(sns); redisTemplate.delete(keys);
	keys := c.GetKeys(ctx, sns)
	if len(keys) == 0 {
		logger.Debugf(ctx, "没有有效的缓存key，无需删除缓存")
		return nil
	}

	// 删除缓存 (遵循规则27: 使用正确的Redis包)
	deletedCount, err := redis.Rdb.Del(ctx, keys...).Result()
	if err != nil {
		logger.Errorfm(ctx, "批量删除SN缓存失败: sns=%v, keys=%v, error=%v", sns, keys, err)
		return err
	}

	logger.Infof(ctx, "批量删除SN缓存成功: sns=%v, deletedCount=%d", sns, deletedCount)
	return nil
}

// RemoveKey 删除指定key的缓存
// 对应 Java: ProductSnDetailActiveCache.removeKey(String key) (遵循规则46: 严格按照Java编码内容转换)
func (c *productSnDetailActiveCache) RemoveKey(ctx context.Context, key string) error {
	// 对应 Java: return redisTemplate.delete(key);
	deletedCount, err := redis.Rdb.Del(ctx, key).Result()
	if err != nil {
		logger.Errorfm(ctx, "删除缓存失败: key=%s, error=%v", key, err)
		return err
	}

	result := deletedCount > 0
	logger.Infof(ctx, "删除缓存完成: key=%s, result=%v", key, result)
	return nil
}

// GetKeys 获取SN缓存key列表
// 对应 Java: ProductSnDetailActiveCache.keys(List<String> sns) (遵循规则46: 严格按照Java编码内容转换)
func (c *productSnDetailActiveCache) GetKeys(ctx context.Context, sns []string) []string {
	// 对应 Java: if (CollectionUtils.isEmpty(sns)) { throw new AppRuntimeException("sn列表不能为空"); }
	if len(sns) == 0 {
		logger.Errorfm(ctx, "sn列表不能为空")
		return []string{}
	}

	// 对应 Java: private static final String nameSpace = "productSnDetailActive::";
	nameSpace := "productSnDetailActive::"
	var keys []string

	// 对应 Java: for (String sn : sns) { builder = new StringBuilder(nameSpace).append("productSnDetailActive_sn_").append(sn); keys.add(builder.toString()); }
	for _, sn := range sns {
		key := fmt.Sprintf("%sproductSnDetailActive_sn_%s", nameSpace, sn)
		keys = append(keys, key)
	}

	return keys
}
