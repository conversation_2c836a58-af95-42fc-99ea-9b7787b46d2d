package cache

import (
	"context"
	"encoding/json"
	"piceacorp.com/device-service/pkg/util"
	"time"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/pkg/data/cache"
)

const (
	// BindKey 缓存前缀 - 对应 Java: DeviceKey.BIND_KEY
	BIND_KEY_PREFIX = "BIND_KEY"
	// BindKey 默认过期时间 (7天) - 对应 Java: SEVEN = 7
	BIND_KEY_DEFAULT_TTL = 7 * 24 * time.Hour
)

// DeviceKey 设备密钥类型枚举
// 对应 Java: DeviceKey enum
type DeviceKey string

const (
	// DeviceKeyUser 设备上报bindKey，服务器存储时，用这个类型
	DeviceKeyUser DeviceKey = "USER"
	// DeviceKeyDevice 扫码时，服务器颁发bindKey时用这个类型
	DeviceKeyDevice DeviceKey = "DEVICE"
)

// BindKey 绑定密钥实体
// 对应 Java: BindKey class
type BindKey struct {
	// 设备ID
	DeviceId string `json:"deviceId"`
	// 绑定密钥
	BindKey string `json:"bindKey"`
	// 基站ID
	BaseId string `json:"baseId"`
	// 基站SN
	BaseSn string `json:"baseSn"`
}

// IBindKeyCache BindKey 缓存接口
// 对应 Java: BindKeyCache class
type IBindKeyCache interface {
	// 获取绑定密钥 - 对应 Java: getBindKey(String id, String tenantId, DeviceKey deviceKey)
	GetBindKey(ctx context.Context, id, tenantId string, deviceKey DeviceKey) (*BindKey, error)
	// 保存绑定密钥 - 对应 Java: saveBindKey(BindKey bindKey, String id, String tenantId, DeviceKey deviceKey)
	SaveBindKey(ctx context.Context, bindKey *BindKey, id, tenantId string, deviceKeyType DeviceKey) error
	// 保存绑定密钥并设置过期时间 - 对应 Java: saveBindKeyWithExpire(...)
	SaveBindKeyWithExpire(ctx context.Context, bindKey *BindKey, id, tenantId string, deviceKeyType DeviceKey, ttl time.Duration) error
	// 删除绑定密钥 - 对应 Java: removeBindKey(String id, String tenantId, DeviceKey deviceKey)
	RemoveBindKey(ctx context.Context, id, tenantId string, deviceKey DeviceKey) error
}

// bindKeyCache BindKey 缓存实现
type bindKeyCache struct{}

var BindKeyCache IBindKeyCache = &bindKeyCache{}

// redisKeyOfBindKey 生成缓存键
// 对应 Java: redisKeyOfBindKey(String id, String tenantId, DeviceKey deviceKey)
// 缓存键格式：
// 1、正常配网的bindKey的格式：BIND_KEY:USER:BIND_KEY_用户id_租户id
// 2、扫码绑定时的bindKey格式：BIND_KEY:DEVICE:BIND_KEY_设备id_租户id
func (c *bindKeyCache) redisKeyOfBindKey(id, tenantId string, deviceKey DeviceKey) string {
	name := string(deviceKey)                                      // "USER" 或 "DEVICE"
	nameSpace := BIND_KEY_PREFIX + ":" + name + ":"                // "BIND_KEY:USER:" 或 "BIND_KEY:DEVICE:"
	return nameSpace + BIND_KEY_PREFIX + "_" + id + "_" + tenantId // "BIND_KEY:USER:BIND_KEY_userId_tenantId"
}

// GetBindKey 获取绑定密钥
// 对应 Java: getBindKey(String id, String tenantId, DeviceKey deviceKey)
func (c *bindKeyCache) GetBindKey(ctx context.Context, id, tenantId string, deviceKey DeviceKey) (*BindKey, error) {
	key := c.redisKeyOfBindKey(id, tenantId, deviceKey)

	result, err := cache.Rdb.Get(ctx, key).Bytes()
	logger.Infof(ctx, "获取绑定密钥: key:%s, result:%s", key, result)
	if err != nil {
		if err.Error() == "redis: nil" {
			logger.Debugf(ctx, "BindKey 不存在: id=%s, tenantId=%s, deviceKey=%s", id, tenantId, deviceKey)
			return nil, nil
		}
		logger.Errorfm(ctx, "获取 BindKey 失败: id=%s, tenantId=%s, deviceKey=%s, error=%v", id, tenantId, deviceKey, err)
		return nil, err
	}

	// 反序列化 BindKey 对象
	bindKey := util.UnmarshalJavaJsonCache[BindKey](result)

	logger.Debugf(ctx, "获取 BindKey 成功: id=%s, tenantId=%s, deviceKey=%s", id, tenantId, deviceKey)
	return bindKey, nil
}

// SaveBindKey 保存绑定密钥 (使用默认过期时间)
// 对应 Java: saveBindKey(BindKey bindKey, String id, String tenantId, DeviceKey deviceKey)
func (c *bindKeyCache) SaveBindKey(ctx context.Context, bindKey *BindKey, id, tenantId string, deviceKeyType DeviceKey) error {
	return c.SaveBindKeyWithExpire(ctx, bindKey, id, tenantId, deviceKeyType, BIND_KEY_DEFAULT_TTL)
}

// SaveBindKeyWithExpire 保存绑定密钥并设置过期时间
// 对应 Java: saveBindKeyWithExpire(BindKey bindKey, String id, String tenantId, DeviceKey deviceKey, Long ttl, TimeUnit timeUnit)
func (c *bindKeyCache) SaveBindKeyWithExpire(ctx context.Context, bindKey *BindKey, id, tenantId string, deviceKeyType DeviceKey, ttl time.Duration) error {
	key := c.redisKeyOfBindKey(id, tenantId, deviceKeyType)

	// 序列化 BindKey 对象
	bindKeyJson, err := json.Marshal(bindKey)
	if err != nil {
		logger.Errorfm(ctx, "序列化 BindKey 失败: id=%s, tenantId=%s, deviceKey=%s, error=%v", id, tenantId, deviceKeyType, err)
		return err
	}

	err = cache.Rdb.Set(ctx, key, bindKeyJson, ttl).Err()
	logger.Infof(ctx, "保存绑定密钥: key:%s, value:%s", key, string(bindKeyJson))
	if err != nil {
		logger.Errorfm(ctx, "保存 BindKey 失败: id=%s, tenantId=%s, deviceKey=%s, ttl=%v, error=%v", id, tenantId, deviceKeyType, ttl, err)
		return err
	}

	logger.Infof(ctx, "保存 BindKey 成功: id=%s, tenantId=%s, deviceKey=%s, ttl=%v", id, tenantId, deviceKeyType, ttl)
	return nil
}

// RemoveBindKey 删除绑定密钥
// 对应 Java: removeBindKey(String id, String tenantId, DeviceKey deviceKey)
func (c *bindKeyCache) RemoveBindKey(ctx context.Context, id, tenantId string, deviceKey DeviceKey) error {
	key := c.redisKeyOfBindKey(id, tenantId, deviceKey)

	result, err := cache.Rdb.Del(ctx, key).Result()
	if err != nil {
		logger.Errorfm(ctx, "删除 BindKey 失败: id=%s, tenantId=%s, deviceKey=%s, error=%v", id, tenantId, deviceKey, err)
		return err
	}

	logger.Infof(ctx, "删除 BindKey 成功: id=%s, tenantId=%s, deviceKey=%s, deleted=%d", id, tenantId, deviceKey, result)
	return nil
}
