package bo

import (
	"time"
)

// DeviceShareHisBo 设备分享历史VO
// 对应 Java: DeviceShareHisBo extends DeviceInviteShareEntity
type DeviceShareHisBo struct {
	// 来自 DeviceInviteShareEntity 的所有字段
	// 主键id
	Id string `gorm:"column:id" json:"id"`
	// 邀请人的用户id
	InviteId string `gorm:"column:invite_id" json:"inviteId"`
	// 被邀请人的用户id
	BeInviteId string `gorm:"column:be_invite_id" json:"beInviteId"`
	// 设备id或家庭id
	TargetId string `gorm:"column:target_id" json:"targetId"`
	// 类型,0-共享设备；1-共享家庭
	Type int `gorm:"column:type" json:"type"`
	// 0-正常；1-已同意；2-已拒绝
	Status int `gorm:"column:status" json:"status"`
	// 0-正常；1-邀请者删除；2-被邀请者删除
	Removed *int `gorm:"column:removed" json:"removed"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间 - 数据库扫描用
	CreateTimeDB time.Time `gorm:"column:create_time" json:"-"`
	// 创建时间 - API返回用（时间戳）
	CreateTime int64 `json:"createTime"`
	// 修改人
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 修改时间 - 数据库扫描用
	UpdateTimeDB time.Time `gorm:"column:update_time" json:"-"`
	// 修改时间 - API返回用（时间戳）
	UpdateTime int64 `json:"updateTime"`
	// 租户id
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	// 区域
	Zone *string `gorm:"column:zone" json:"zone"`

	// DeviceShareHisBo 自有字段
	// 用户名,当前用户为分享者，则用户名为接受分享者的用户名(分享给:xxx)；当前用户为接受分享者，则用户名为分享者的用户名（来自:xxx）,密文，app端自己解密
	Username string `json:"username"`
	// 设备sn
	Sn *string `gorm:"column:sn" json:"sn"`
	// 设备mac
	Mac *string `gorm:"column:mac" json:"mac"`
	// 设备虚拟id
	IotId *string `gorm:"column:iot_id" json:"iotId"`
	// 设备昵称或家庭名称
	Name *string `gorm:"column:name" json:"name"`
	// 产品型号代码
	ProductModeCode *string `gorm:"column:product_mode_code" json:"productModeCode"`
	// 产品型号名称
	ModeType string `json:"modeType"`
	// 图片地址(用户的图片会加密，设备的图片不会加密)
	PhotoUrl string `json:"photoUrl"`
	// 用户头像URL
	AvatarUrl string `json:"avatarUrl"`
	// 产品ID
	ProductId *string `json:"productId"`
	// 产品名称
	ProductName string `json:"productName"`
}
