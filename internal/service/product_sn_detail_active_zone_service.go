package service

import (
	"context"
	"time"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IProductSnDetailActiveZoneService SN激活服务接口
// 对应 Java: IProductSnDetailActiveZoneService (遵循规则46: 严格按照Java编码内容转换)
type IProductSnDetailActiveZoneService interface {
	// 根据SN查询激活信息 - 对应 Java: ProductSnDetailActiveZoneEntity getBySn(String sn);
	GetBySn(ctx context.Context, sn string) (*domain.ProductSnDetailActiveZone, error)
	// 插入激活信息 - 对应 Java: ResponseMessage<Boolean> insertOne(ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity);
	InsertOne(ctx context.Context, entity *domain.ProductSnDetailActiveZone) webRes.IBaseRes
	// 根据SN删除激活信息 - 对应 Java: ResponseMessage<Boolean> delBySn(String sn);
	DelBySn(ctx context.Context, sn string) webRes.IBaseRes
	// 根据SN列表删除激活信息 - 对应 Java: ResponseMessage<Boolean> delBySns(List<String> sns);
	DelBySns(ctx context.Context, sns []string) webRes.IBaseRes
	// 根据组ID统计数量 - 对应 Java: Long countByGroupId(String groupId);
	CountByGroupId(ctx context.Context, groupId string) (int64, error)
	// 模糊查询SN - 对应 Java: List<ProductSnDetailActiveZoneEntity> snCodes(String productModeId, String sn);
	SnCodes(ctx context.Context, productModeId, sn string) ([]*domain.ProductSnDetailActiveZone, error)
	// 根据SN列表查询 - 对应 Java: List<ProductSnDetailActiveZoneEntity> getBySnList(List<String> snList);
	GetBySnList(ctx context.Context, snList []string) ([]*domain.ProductSnDetailActiveZone, error)
}

var ProductSnDetailActiveZoneService productSnDetailActiveZoneService

type productSnDetailActiveZoneService struct{}

// GetBySn 根据SN查询激活信息
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.getBySn(String sn) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) GetBySn(ctx context.Context, sn string) (*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "根据SN查询激活信息: sn=%s", sn)

	// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn); return productSnDetailActiveZoneMapper.selectOne(wrapper);
	entity, err := repository.ProductSnDetailActiveZoneRepository.GetBySn(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "根据SN查询激活信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	logger.Infof(ctx, "根据SN查询激活信息成功: sn=%s, found=%v", sn, entity != nil)
	return entity, nil
}

// InsertOne 插入激活信息
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.insertOne(ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) InsertOne(ctx context.Context, entity *domain.ProductSnDetailActiveZone) webRes.IBaseRes {
	logger.Infof(ctx, "插入激活信息: sn=%s, id=%s", entity.Sn, entity.Id)

	// 对应 Java: if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getSn())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn"); }
	if entity.Sn == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
	}

	// 对应 Java: if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getKey())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "key"); }
	if entity.Key == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "key")
	}

	// 对应 Java: if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getProductModeId())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "productModeId"); }
	if entity.ProductModeId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "productModeId")
	}

	// 对应 Java: if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getId())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "id"); }
	if entity.Id == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "id")
	}

	// 对应 Java: productSnDetailActiveZoneEntity.setTenantId(TenantIdHandleUtil.getTenantId(productSnDetailActiveZoneEntity.getTenantId()));
	if entity.TenantId == "" {
		entity.TenantId = ctx.Value(ctxKeys.TENANT_ID).(string)
	}

	// 设置创建时间
	entity.CreateTime = time.Now()

	// 对应 Java: int insert = productSnDetailActiveZoneMapper.insert(productSnDetailActiveZoneEntity); return ResponseMessage.buildSuccess(insert > 0);
	err := repository.ProductSnDetailActiveZoneRepository.Insert(ctx, entity)
	if err != nil {
		logger.Errorfm(ctx, "插入激活信息失败: sn=%s, id=%s, error=%v", entity.Sn, entity.Id, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "插入激活信息失败")
	}

	logger.Infof(ctx, "插入激活信息成功: sn=%s, id=%s", entity.Sn, entity.Id)
	return webRes.Cb(true)
}

// DelBySn 根据SN删除激活信息
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.delBySn(String sn) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) DelBySn(ctx context.Context, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "根据SN删除激活信息: sn=%s", sn)

	// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn); int delete = productSnDetailActiveZoneMapper.delete(wrapper); return ResponseMessage.buildSuccess(delete > 0);
	deleteCount, err := repository.ProductSnDetailActiveZoneRepository.DeleteBySn(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "根据SN删除激活信息失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除激活信息失败")
	}

	result := deleteCount > 0
	logger.Infof(ctx, "根据SN删除激活信息成功: sn=%s, deleteCount=%d, result=%v", sn, deleteCount, result)
	return webRes.Cb(result)
}

// DelBySns 根据SN列表删除激活信息
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.delBySns(List<String> sns) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) DelBySns(ctx context.Context, sns []string) webRes.IBaseRes {
	logger.Infof(ctx, "根据SN列表删除激活信息: sns=%v", sns)

	// 对应 Java: if(CollectionUtils.isEmpty(sns)){ return ResponseMessage.buildSuccess(false); }
	if len(sns) == 0 {
		return webRes.Cb(false)
	}

	// 对应 Java: QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().in(ProductSnDetailActiveZoneEntity::getSn, sns); int delete = productSnDetailActiveZoneMapper.delete(wrapper);
	deleteCount, err := repository.ProductSnDetailActiveZoneRepository.DeleteBySns(ctx, sns)
	if err != nil {
		logger.Errorfm(ctx, "根据SN列表删除激活信息失败: sns=%v, error=%v", sns, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除激活信息失败")
	}

	// 对应 Java: if (delete > 0) { activeCache.removeCacheBySns(sns); }
	if deleteCount > 0 {
		err = cache.ProductSnDetailActiveCache.RemoveCacheBySns(ctx, sns)
		if err != nil {
			logger.Errorfm(ctx, "删除缓存失败: sns=%v, error=%v", sns, err)
			// 缓存删除失败不影响主流程
		}
	}

	result := deleteCount > 0
	logger.Infof(ctx, "根据SN列表删除激活信息成功: sns=%v, deleteCount=%d, result=%v", sns, deleteCount, result)
	return webRes.Cb(result)
}

// CountByGroupId 根据组ID统计数量
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.countByGroupId(String groupId) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) CountByGroupId(ctx context.Context, groupId string) (int64, error) {
	logger.Infof(ctx, "根据组ID统计数量: groupId=%s", groupId)

	// 对应 Java: LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> activeWrapper = new LambdaQueryWrapper<>(); activeWrapper.eq(ProductSnDetailActiveZoneEntity::getGroupId, groupId); return this.count(activeWrapper);
	count, err := repository.ProductSnDetailActiveZoneRepository.CountByGroupId(ctx, groupId)
	if err != nil {
		logger.Errorfm(ctx, "根据组ID统计数量失败: groupId=%s, error=%v", groupId, err)
		return 0, err
	}

	logger.Infof(ctx, "根据组ID统计数量成功: groupId=%s, count=%d", groupId, count)
	return count, nil
}

// SnCodes 模糊查询SN
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.snCodes(String productModeId, String sn) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) SnCodes(ctx context.Context, productModeId, sn string) ([]*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "模糊查询SN: productModeId=%s, sn=%s", productModeId, sn)

	// 对应 Java: return baseMapper.snCodes(productModeId, sn);
	entities, err := repository.ProductSnDetailActiveZoneRepository.SnCodes(ctx, productModeId, sn)
	if err != nil {
		logger.Errorfm(ctx, "模糊查询SN失败: productModeId=%s, sn=%s, error=%v", productModeId, sn, err)
		return nil, err
	}

	logger.Infof(ctx, "模糊查询SN成功: productModeId=%s, sn=%s, count=%d", productModeId, sn, len(entities))
	return entities, nil
}

// GetBySnList 根据SN列表查询
// 对应 Java: ProductSnDetailActiveZoneServiceImpl.getBySnList(List<String> snList) (遵循规则46: 严格按照Java编码内容转换)
func (s *productSnDetailActiveZoneService) GetBySnList(ctx context.Context, snList []string) ([]*domain.ProductSnDetailActiveZone, error) {
	logger.Infof(ctx, "根据SN列表查询: snList=%v", snList)

	// 对应 Java: LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> lambda = new QueryWrapper<ProductSnDetailActiveZoneEntity>().lambda(); lambda.in(ProductSnDetailActiveZoneEntity::getSn, snList); return this.list(lambda);
	entities, err := repository.ProductSnDetailActiveZoneRepository.GetBySnList(ctx, snList)
	if err != nil {
		logger.Errorfm(ctx, "根据SN列表查询失败: snList=%v, error=%v", snList, err)
		return nil, err
	}

	logger.Infof(ctx, "根据SN列表查询成功: snList=%v, count=%d", snList, len(entities))
	return entities, nil
}
