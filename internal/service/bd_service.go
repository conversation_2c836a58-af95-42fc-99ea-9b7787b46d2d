package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"github.com/spf13/viper"
	internal_util "piceacorp.com/device-service/internal/util"
	"piceacorp.com/device-service/pkg/util"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/dto"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/internal/service/remote"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IBdService 百度服务接口
// 对应 Java: IBdService (遵循规则46: 严格按照Java编码内容转换)
type IBdService interface {
	// 百度技能处理 - 对应 Java: BdSkillResponse skill(String jsonStr, String authorization, String accessKey, String timestamp);
	Skill(ctx context.Context, jsonStr, authorization, accessKey, timestamp string) *vo.BdSkillResponse
	// 获取回复词 - 对应 Java: ResponseMessage<List<String>> getReply(LargeModelParam param);
	GetReply(ctx context.Context, param *dto.LargeModelParam) webRes.IBaseRes
}

var BdService bdService

type bdService struct{}

// 常量定义 - 对应 Java: 类中的常量
const (
	WATER_STRING                   = "当前为较干水量。"
	WIND_STRING                    = "当前吸力为关。"
	RESERVED_SIDE_BRUSH_KEY_STRING = ",预计还可使用"
	VOLUME_STRING                  = "当前音量低。"
	OK                             = "好的"
	NOT_FOUND                      = "notfound"
	BATTERY                        = "battery"
	QUANTITY_KEY                   = "quantity"
	LOCATION_SLOT                  = "location"
	FURNITURE_SLOT                 = "furniture"
	STRING_TYPE                    = "STRING"
	TIME_TYPE                      = "TIME"
	BEGIN                          = "begin"
	END                            = "end"
	CONSUMABLE                     = "consumable"
	WIND                           = "wind"
	WATER                          = "water"
	VOLUME                         = "volume"
	RESERVED_MAIN_BRUSH_KEY        = "reserved_main_brush"
	RESERVED_SIDE_BRUSH_KEY        = "reserved_side_brush"
	RESERVED_HYPA_KEY              = "reserved_hypa"
	RESERVED_MOP_LIFE_KEY          = "reserved_mop_life"

	// 对应 Java中的常量: private static final String SECRET_KEY = "e17a11a870be6775342d822828f8c8d3";
	SECRET_KEY = "e17a11a870be6775342d822828f8c8d3"
	// 对应 Java中的常量: private static final String ACCESS_KEY = "935cf088427cbe99";
	ACCESS_KEY = "935cf088427cbe99"
)

// 对应 Java中的常量: private static final Pattern PATTERN = Pattern.compile("\\#\\{([a-zA-Z0-9_]+?)}");
var PATTERN = regexp.MustCompile(`\#\{([a-zA-Z0-9_]+?)\}`)

// Skill 百度技能处理
// 对应 Java: BdServiceImpl.skill(String jsonStr, String authorization, String accessKey, String timestamp) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) Skill(ctx context.Context, jsonStr, authorization, accessKey, timestamp string) *vo.BdSkillResponse {
	logger.Infof(ctx, "百度技能处理: jsonStr=%s", jsonStr)

	response := &vo.BdSkillResponse{}
	var param vo.BdSkillParam

	// 对应 Java: try { param = JSON.parseObject(jsonStr, BdSkillParam.class); } catch (Exception e) { response.setErrCode(400); return response; }
	err := json.Unmarshal([]byte(jsonStr), &param)
	if err != nil {
		logger.Errorfm(ctx, "入参json字符串转换异常: jsonStr=%s, error=%v", jsonStr, err)
		response.ErrCode = 400
		return response
	}

	// 对应 Java: if (!authenticateRequest(timestamp, authorization, accessKey, jsonStr)) { response.setErrCode(1001); return response; }
	if !s.authenticateRequest(ctx, timestamp, authorization, accessKey, jsonStr) {
		response.ErrCode = 1001
		return response
	}

	// 对应 Java: String conversationId = SnowflakeUtil.snowflakeId();
	conversationId := util.Id()

	// 对应 Java: BeanUtil.copyProperties(param, response);
	response.LogId = param.LogId
	response.Device = param.Device
	response.Query = param.Query
	response.NluInfos = param.NluInfos
	response.ExtInfo = param.ExtInfo
	response.Custom = param.Custom
	response.ConversationId = conversationId

	response.ErrCode = 0
	response.Tts = s.getTts(ctx, &param)

	// 对应 Java: if(StringUtils.isNotBlank(response.getTts().getContent()) && !LARGEFLAGREPLYTEXT.equals(response.getTts().getContent())){ ... }
	largeModelReplyText := viper.GetString("largemodel.replytext")
	if largeModelReplyText == "" {
		largeModelReplyText = "大模型处理中"
	}
	if response.Tts.Content != "" && response.Tts.Content != largeModelReplyText {
		// 保存会话记录
		conversation := &domain.Conversation{
			Id:         conversationId,
			Sn:         param.Device.Ak,
			CreateTime: time.Now(),
		}

		// 对应 Java: JSONObject data = new JSONObject(); data.put("1", param.getQuery()); data.put("2", response.getTts().getContent()); conversation.setConversation(data.toJSONString());
		conversationData := map[string]string{
			"1": param.Query,
			"2": response.Tts.Content,
		}
		conversationJSON, _ := json.Marshal(conversationData)
		conversation.Conversation = string(conversationJSON)

		err = repository.ConversationRepository.Insert(ctx, conversation)
		if err != nil {
			logger.Errorfm(ctx, "保存会话记录失败: conversationId=%s, error=%v", conversationId, err)
		}
	}

	logger.Infof(ctx, "百度技能处理完成: conversationId=%s, errCode=%d", conversationId, response.ErrCode)
	return response
}

// GetReply 获取回复词
// 对应 Java: BdServiceImpl.getReply(LargeModelParam param) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) GetReply(ctx context.Context, param *dto.LargeModelParam) webRes.IBaseRes {
	logger.Infof(ctx, "获取回复词: ak=%s, nluInfos=%v", param.Ak, param.NluInfos)

	// 对应 Java: if(StringUtils.isBlank(sn)){ return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn不能为空"); }
	if param.Ak == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn不能为空")
	}

	// 对应 Java: if(CollUtil.isEmpty(param.getNluInfos())){ return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "nluInfos不能为空"); }
	if len(param.NluInfos) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "nluInfos不能为空")
	}

	var reply []string
	// 对应 Java: for (int i = 0; i < param.getNluInfos().size(); i++) { JSONObject intentJson = param.getNluInfos().get(i); String rw = generateReplyWord(intentJson, sn); reply.add(rw); }
	for _, intentJson := range param.NluInfos {
		rw := s.generateReplyWord(ctx, intentJson, param.Ak)
		reply = append(reply, rw)
	}

	// 对应 Java: return ResponseMessage.buildSuccess(reply);
	logger.Infof(ctx, "获取回复词成功: ak=%s, replyCount=%d", param.Ak, len(reply))
	return webRes.Cb(reply)
}

// authenticateRequest 请求鉴权
// 对应 Java: authenticateRequest(String timestamp, String authorization, String accessKey, String jsonStr) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) authenticateRequest(ctx context.Context, timestamp, authorization, accessKey, jsonStr string) bool {
	// 对应 Java: if (StringUtils.isBlank(timestamp) || StringUtils.isBlank(authorization) || StringUtils.isBlank(accessKey)) { return false; }
	if timestamp == "" || authorization == "" || accessKey == "" {
		return false
	}

	// 对应 Java: long timeDiff = Math.abs(now - currentTime); if (timeDiff > 5 * 50 * 1000) { return false; }
	timestampLong, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return false
	}
	currentTime := time.Now().UnixMilli()
	timeDiff := abs(currentTime - timestampLong)
	if timeDiff > 250000 { // 对应Java中的 5 * 50 * 1000
		return false
	}

	// 对应 Java: String calculatedSignature = calculateSignature(timestamp, accessKey, requestBody);
	calculatedSignature := s.calculateSignature(timestamp, accessKey, jsonStr)

	// 对应 Java: LogUtils.info("authenticateRequest-calculatedSignature:{}",calculatedSignature);
	logger.Infof(ctx, "authenticateRequest-calculatedSignature: %s", calculatedSignature)

	// 对应 Java: return signature.equals(calculatedSignature);
	return authorization == calculatedSignature
}

// calculateSignature 计算签名
// 对应 Java: calculateSignature(String timestamp, String accessKey, String requestBody)
func (s *bdService) calculateSignature(timestamp, accessKey, requestBody string) string {
	// 对应 Java: Mac mac = Mac.getInstance("HmacSHA256");
	// SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
	// mac.init(secretKeySpec);
	// String toBeSigned = accessKey + timestamp + requestBody;
	// byte[] signatureBytes = mac.doFinal(toBeSigned.getBytes(StandardCharsets.UTF_8));
	// return Base64.getEncoder().encodeToString(signatureBytes);

	toBeSigned := accessKey + timestamp + requestBody
	h := hmac.New(sha256.New, []byte(SECRET_KEY))
	h.Write([]byte(toBeSigned))
	signatureBytes := h.Sum(nil)
	return base64.StdEncoding.EncodeToString(signatureBytes)
}

// getTts 获取TTS内容
// 对应 Java: getTts(BdSkillParam param) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) getTts(ctx context.Context, param *vo.BdSkillParam) *vo.Tts {
	var sb strings.Builder
	sn := param.Device.Ak

	// 对应 Java: getReply(nluInfos, sn, sb);
	s.getReply(ctx, param.NluInfos, sn, &sb)

	tts := &vo.Tts{
		Content: sb.String(),
		Flag:    0,
	}

	return tts
}

// getReply 处理回复词
// 对应 Java: getReply(String nluInfos, String sn, StringBuilder sb) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) getReply(ctx context.Context, nluInfos, sn string, sb *strings.Builder) {
	// 对应 Java: JSONArray intentArray = JSON.parseArray(nluInfos);
	var intentArray []map[string]interface{}
	err := json.Unmarshal([]byte(nluInfos), &intentArray)
	if err != nil {
		logger.Errorfm(ctx, "解析nluInfos失败: nluInfos=%s, error=%v", nluInfos, err)
		return
	}

	// 对应 Java: for (int i = 0; i < intentArray.size(); i++) { ... }
	for i, intentJson := range intentArray {
		// 对应 Java: String rw = generateReplyWord(intentJson, sn);
		rw := s.generateReplyWord(ctx, intentJson, sn)

		// 对应 Java: if (i != 0) { if (rw.startsWith(OK)) { ... } rw = LARGEFLAGREPLYTEXT.equals(rw) ? "" : rw; }
		if i != 0 {
			if strings.HasPrefix(rw, OK) {
				index := 2
				for index < len(rw) {
					if !s.isNotChineseDigitOrAsciiLetter(rune(rw[index])) {
						break
					}
					index++
				}
				if index < len(rw) {
					rw = rw[index:]
				} else {
					rw = ""
				}
			}
			largeModelReplyText := viper.GetString("largemodel.replytext")
			if largeModelReplyText == "" {
				largeModelReplyText = "大模型处理中"
			}
			if rw == largeModelReplyText {
				rw = ""
			}
		}
		sb.WriteString(rw)
	}
}

// generateReplyWord 生成回复词
// 对应 Java: generateReplyWord(JSONObject intentJson, String sn) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) generateReplyWord(ctx context.Context, intentJson map[string]interface{}, sn string) string {
	replyWord := ""
	domain, _ := intentJson["domain"].(string)
	intent, _ := intentJson["intent"].(string)

	// 对应 Java: if(Arrays.asList(largemodelDomain.split(",")).contains(domain)){ replyWord = LARGEFLAGREPLYTEXT; return replyWord; }
	largemodelDomain := viper.GetString("largemodel.domain")
	if largemodelDomain == "" {
		largemodelDomain = "greeting,failure"
	}
	domains := strings.Split(largemodelDomain, ",")
	for _, d := range domains {
		if d == domain {
			replyWord = viper.GetString("largemodel.replytext")
			if replyWord == "" {
				replyWord = "大模型处理中"
			}
			return replyWord
		}
	}

	// 对应 Java: JSONObject slots = intentJson.getJSONObject("slots") !=null ? intentJson.getJSONObject("slots") : new JSONObject();
	slots, ok := intentJson["slots"].(map[string]interface{})
	if !ok {
		slots = make(map[string]interface{})
	}

	// 对应 Java: StandardEvaluationContext context = new StandardEvaluationContext();
	// Map<String, String> replaceMap = new HashMap<>(16);
	context := make(map[string]interface{})
	replaceMap := make(map[string]string)

	// 对应 Java: 处理位置是否存在
	// String s1 = duelNotFound(domain, intent, slots);
	// if (StringUtils.isNotBlank(s1)) { return s1; }
	notFound := s.duelNotFound(ctx, domain, intent, slots)
	if notFound != "" {
		return notFound
	}

	// 对应 Java: List<String> slotList = new ArrayList<>();
	var slotList []string

	// 对应 Java: for (String key : slots.keySet()) { slotList.add(key); JSONArray slotJsonArray = slots.getJSONArray(key); context.setVariable(key, slotJsonArray.size()); }
	for key, value := range slots {
		slotList = append(slotList, key)
		slotArray, ok := value.([]interface{})
		if ok {
			context[key] = len(slotArray)
		}
	}

	// 对应 Java: setReplaceMap(replaceMap, slots);
	s.setReplaceMap(ctx, replaceMap, slots)

	// 对应 Java: 查询回复词模板
	// 对应 Java: LambdaQueryWrapper<ReplyWords> queryWrapper = new LambdaQueryWrapper<>();
	// queryWrapper.eq(ReplyWords::getDomain, domain)
	// .eq(ReplyWords::getHandleFlag, 0)
	// .eq(ReplyWords::getIntention, intent)
	// .ne(ReplyWords::getConditionExpression, NOT_FOUND)
	// .orderByAsc(ReplyWords::getPriority);
	//
	// if (CollectionUtils.isNotEmpty(slotList)) {
	//     queryWrapper.in(ReplyWords::getSlot, slotList);
	// }
	//
	// List<ReplyWords> replyWords1 = replyWordsMapper.selectList(queryWrapper);
	replyWords, err := repository.ReplyWordsRepository.FindByDomainAndIntent(ctx, domain, intent, NOT_FOUND)
	if err != nil {
		logger.Errorfm(ctx, "查询回复词模板失败: domain=%s, intent=%s, error=%v", domain, intent, err)
		return replyWord
	}

	// 对应 Java: LogUtils.info("获取回复词模板：{}",JSON.toJSONString(replyWords1));
	logger.Infof(ctx, "获取回复词模板: domain=%s, intent=%s, count=%d", domain, intent, len(replyWords))

	// 对应 Java: if(CollectionUtils.isEmpty(replyWords1)){ return replyWord; }
	if len(replyWords) == 0 {
		logger.Infof(ctx, "未找到匹配的回复词模板: domain=%s, intent=%s", domain, intent)
		return replyWord
	}

	// 对应 Java: 使用优先级排序的结果，一般第一条就是正确的匹配
	// 我们已经在查询时按优先级排序了，这里直接按顺序处理
	for _, replyWordEntity := range replyWords {
		// 对应 Java: try {
		// String conditionExpression = replyWords.getConditionExpression();
		// if (StringUtils.isNotBlank(conditionExpression)) {
		conditionExpression := replyWordEntity.ConditionExpression
		if conditionExpression != "" && conditionExpression != NOT_FOUND {

			// 求值表达式
			result, parseErr := internal_util.EvaluateExpression(ctx, conditionExpression, context)
			if parseErr != nil {
				logger.Errorfm(ctx, "解析条件表达式失败: expression=%s, error=%v", conditionExpression, parseErr)
				continue
			}

			if !result {
				// 条件不满足，跳过此模板
				continue
			}

			// 匹配成功，使用此模板
			replyWord = replyWordEntity.ReplyWord

			matches := PATTERN.FindAllStringSubmatch(replyWord, -1)
			for _, match := range matches {
				if len(match) > 1 {
					fieldName := match[1]
					replacement := ""
					// 优先使用replaceMap中的值
					if val, ok := replaceMap[fieldName]; ok && val != "" {
						replacement = val
					} else {
						// 如果replaceMap中没有，则获取设备信息
						replacement = s.getDeviceInfo(ctx, sn, fieldName)
					}
					replyWord = strings.ReplaceAll(replyWord, match[0], replacement)
				}
			}
			break
		} else {
			// 如果没有条件表达式，直接使用模板
			// 对应 Java: replyWord = replyWords.getReplyWord();
			replyWord = replyWordEntity.ReplyWord

			matches := PATTERN.FindAllStringSubmatch(replyWord, -1)
			for _, match := range matches {
				if len(match) > 1 {
					fieldName := match[1]
					replacement := ""
					// 优先使用replaceMap中的值
					if val, ok := replaceMap[fieldName]; ok && val != "" {
						replacement = val
					} else {
						// 如果replaceMap中没有，则获取设备信息
						replacement = s.getDeviceInfo(ctx, sn, fieldName)
					}
					replyWord = strings.ReplaceAll(replyWord, match[0], replacement)
				}
			}
			break
		}
	}

	// 对应 Java: LogUtils.info("获取回复词：{}",replyWord);
	logger.Infof(ctx, "生成回复词: domain=%s, intent=%s, replyWord=%s", domain, intent, replyWord)
	return replyWord
}

// duelNotFound 处理位置是否存在
// 对应 Java: duelNotFound(String domain, String intent, JSONObject slots)
func (s *bdService) duelNotFound(ctx context.Context, domain, intent string, slots map[string]interface{}) string {
	if slots == nil {
		return ""
	}

	// 处理位置槽位
	location := s.duelNotFoundForSlot(ctx, domain, intent, slots, LOCATION_SLOT)
	if location != "" {
		return location
	}

	// 处理家具槽位
	return s.duelNotFoundForSlot(ctx, domain, intent, slots, FURNITURE_SLOT)
}

// duelNotFoundForSlot 处理特定槽位是否存在
// 对应 Java: duelNotFound(String domain, String intent, JSONObject slots, String slot)
func (s *bdService) duelNotFoundForSlot(ctx context.Context, domain, intent string, slots map[string]interface{}, slotName string) string {
	slotArray, ok := slots[slotName].([]interface{})
	if !ok || len(slotArray) == 0 {
		return ""
	}

	for _, slotItem := range slotArray {
		slotObj, ok := slotItem.(map[string]interface{})
		if !ok {
			continue
		}

		value, _ := slotObj["value"].(string)
		text, _ := slotObj["text"].(string)

		if value != "" && value == text {
			// 查询不存在位置的回复词模板
			replyWords, err := repository.ReplyWordsRepository.FindByDomainAndIntentWithNotFound(ctx, domain, intent, slotName, NOT_FOUND)
			if err != nil {
				logger.Errorfm(ctx, "查询不存在位置回复词模板失败: domain=%s, intent=%s, slot=%s, error=%v",
					domain, intent, slotName, err)
				return ""
			}

			if len(replyWords) > 0 {
				return replyWords[0].ReplyWord
			}
		}
	}

	return ""
}

// setReplaceMap 设置替换词映射
// 对应 Java: setReplaceMap(Map<String, String> replaceMap, JSONObject slots)
func (s *bdService) setReplaceMap(ctx context.Context, replaceMap map[string]string, slots map[string]interface{}) {
	for key, value := range slots {
		slotArray, ok := value.([]interface{})
		if !ok || len(slotArray) == 0 {
			continue
		}

		// 获取槽位类型
		slotType := ""
		if len(slotArray) > 0 {
			slotObj, ok := slotArray[0].(map[string]interface{})
			if ok {
				slotType, _ = slotObj["slot_type"].(string)
			}
		}

		// 处理字符串类型槽位
		if slotType == STRING_TYPE {
			var slotValue strings.Builder

			for i, item := range slotArray {
				slotObj, ok := item.(map[string]interface{})
				if !ok {
					continue
				}
				if i > 0 {
					slotValue.WriteString("、")
				}
				text, _ := slotObj["text"].(string)
				slotValue.WriteString(text)
			}

			replaceMap[key] = slotValue.String()
		} else if slotType == TIME_TYPE {
			// 处理时间类型槽位
			if len(slotArray) > 0 {
				timeSlot, ok := slotArray[0].(map[string]interface{})
				if !ok {
					continue
				}

				valueObj, ok := timeSlot["value"].(map[string]interface{})
				if !ok {
					continue
				}

				// 处理开始时间
				if begin, ok := valueObj[BEGIN].(map[string]interface{}); ok {
					if date, ok := begin["date"].(string); ok {
						replaceMap["time.begin.date"] = date
					}
					if time, ok := begin["time"].(string); ok {
						replaceMap["time.begin.time"] = time
					}
				} else if end, ok := valueObj[END].(map[string]interface{}); ok {
					// 处理结束时间
					if date, ok := end["date"].(string); ok {
						replaceMap["time.end.date"] = date
					}
					if time, ok := end["time"].(string); ok {
						replaceMap["time.end.time"] = time
					}
				}
			}
		}
	}
}

// getDeviceInfo 获取设备信息
// 对应 Java: getDeviceInfo(String sn, String fieldName) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) getDeviceInfo(ctx context.Context, sn, fieldName string) string {
	// 对应 Java: ResponseMessage<DeviceShadowEntity> response = deviceShadowRemote.findBySN(sn);
	deviceShadow, err := remote.DeviceShadowService.FindBySN(ctx, sn)

	// 对应 Java: LogUtils.info("设备属性查询结果：{}：sn:{}", JSON.toJSONString(response),sn);
	logger.Infof(ctx, "设备属性查询结果: sn=%s, error=%v", sn, err)

	if err != nil || deviceShadow == nil {
		logger.Warnf(ctx, "获取设备属性失败: sn=%s, error=%v", sn, err)
		return ""
	}

	// 将Properties转换成具体的map类型，方便访问
	var propertiesMap map[string]interface{}
	if deviceShadow.Properties != nil {
		// 如果是JSON字符串，先解析
		if propStr, ok := deviceShadow.Properties.(string); ok {
			if err := json.Unmarshal([]byte(propStr), &propertiesMap); err != nil {
				logger.Errorfm(ctx, "解析设备属性失败: sn=%s, error=%v", sn, err)
				return ""
			}
		} else if propMap, ok := deviceShadow.Properties.(map[string]interface{}); ok {
			// 如果已经是map，直接使用
			propertiesMap = propMap
		}
	}

	// 如果属性为空，直接返回空字符串
	if propertiesMap == nil {
		return ""
	}

	// 对应 Java: 获取电池电量 - if (BATTERY.equals(fieldName))
	if fieldName == BATTERY {
		// 对应 Java: if (response.isSuccess()) { DeviceShadowEntity deviceShadowEntity = response.getResult();
		// if (deviceShadowEntity != null) { JSONObject properties = deviceShadowEntity.getProperties();
		// if (properties != null) { String battery = properties.getString(QUANTITY_KEY);
		// return StringUtils.isNotBlank(battery) ? battery : ""; } } }
		if battery, ok := propertiesMap[QUANTITY_KEY].(string); ok && battery != "" {
			return battery
		}
		// 可能是数字类型
		if batteryNum, ok := propertiesMap[QUANTITY_KEY].(float64); ok {
			return strconv.Itoa(int(batteryNum))
		} else if batteryNum, ok := propertiesMap[QUANTITY_KEY].(int); ok {
			return strconv.Itoa(batteryNum)
		}
		return ""
	} else if fieldName == CONSUMABLE {
		// 获取耗材信息 - 对应 Java: StringBuilder sb = new StringBuilder();
		var sb strings.Builder

		// 处理主刷 - 对应 Java: Integer use1 = properties.getInteger(RESERVED_MAIN_BRUSH_KEY);
		// if (use1 != null) { sb.append("机器人主刷已使用百分之").append(use1*100/360).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(360 - use1, 0)).append("小时。"); }
		use1 := getIntegerProperty(propertiesMap, RESERVED_MAIN_BRUSH_KEY)
		if use1 != nil {
			sb.WriteString("机器人主刷已使用百分之")
			sb.WriteString(strconv.Itoa((*use1) * 100 / 360))
			sb.WriteString(RESERVED_SIDE_BRUSH_KEY_STRING)
			sb.WriteString(strconv.Itoa(max(360-(*use1), 0)))
			sb.WriteString("小时。")
		}

		// 处理边刷 - 对应 Java: Integer use2 = properties.getInteger(RESERVED_SIDE_BRUSH_KEY);
		// if (use2 != null) { sb.append("机器人边刷已使用百分之").append(use2*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use2, 0)).append("小时。"); }
		use2 := getIntegerProperty(propertiesMap, RESERVED_SIDE_BRUSH_KEY)
		if use2 != nil {
			sb.WriteString("机器人边刷已使用百分之")
			sb.WriteString(strconv.Itoa((*use2) * 100 / 180))
			sb.WriteString(RESERVED_SIDE_BRUSH_KEY_STRING)
			sb.WriteString(strconv.Itoa(max(180-(*use2), 0)))
			sb.WriteString("小时。")
		}

		// 处理尘盒滤网 - 对应 Java: Integer use3 = properties.getInteger(RESERVED_HYPA_KEY);
		// if (use3 != null) { sb.append("机器人尘盒滤网已使用百分之").append(use3*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use3, 0)).append("小时。"); }
		use3 := getIntegerProperty(propertiesMap, RESERVED_HYPA_KEY)
		if use3 != nil {
			sb.WriteString("机器人尘盒滤网已使用百分之")
			sb.WriteString(strconv.Itoa((*use3) * 100 / 180))
			sb.WriteString(RESERVED_SIDE_BRUSH_KEY_STRING)
			sb.WriteString(strconv.Itoa(max(180-(*use3), 0)))
			sb.WriteString("小时。")
		}

		// 处理滚筒拖布 - 对应 Java: Integer use4 = properties.getInteger(RESERVED_MOP_LIFE_KEY);
		// if (use4 != null) { sb.append("机器人滚筒拖布已使用百分之").append(use4*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use4, 0)).append("小时。"); }
		use4 := getIntegerProperty(propertiesMap, RESERVED_MOP_LIFE_KEY)
		if use4 != nil {
			sb.WriteString("机器人滚筒拖布已使用百分之")
			sb.WriteString(strconv.Itoa((*use4) * 100 / 180))
			sb.WriteString(RESERVED_SIDE_BRUSH_KEY_STRING)
			sb.WriteString(strconv.Itoa(max(180-(*use4), 0)))
			sb.WriteString("小时。")
		}

		return sb.String()
	} else if fieldName == WIND {
		// 获取吸力 - 对应 Java: return getWindString(response);
		return s.getWindString(propertiesMap)
	} else if fieldName == WATER {
		// 获取水量 - 对应 Java: return getWaterString(response);
		return s.getWaterString(propertiesMap)
	} else if fieldName == VOLUME {
		// 获取音量 - 对应 Java: return getVolumeString(response);
		return s.getVolumeString(propertiesMap)
	}

	return ""
}

// getIntegerProperty 尝试从属性中获取整数值，处理不同类型转换
func getIntegerProperty(properties map[string]interface{}, key string) *int {
	if val, ok := properties[key]; ok {
		// 处理整数类型
		if intVal, ok := val.(int); ok {
			return &intVal
		}

		// 处理浮点数类型
		if floatVal, ok := val.(float64); ok {
			intVal := int(floatVal)
			return &intVal
		}

		// 处理字符串类型
		if strVal, ok := val.(string); ok {
			if intVal, err := strconv.Atoi(strVal); err == nil {
				return &intVal
			}
		}
	}
	return nil
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// getWindString 获取吸力字符串
// 对应 Java: getWindString(ResponseMessage<DeviceShadowEntity> response)
func (s *bdService) getWindString(propertiesMap map[string]interface{}) string {
	var sb strings.Builder

	// 对应 Java: if (!response.isSuccess()) { sb.append(WINDSTRING); return sb.toString(); }
	// 对应 Java: DeviceShadowEntity deviceShadowEntity = response.getResult();
	// if (deviceShadowEntity == null) { sb.append(WINDSTRING); return sb.toString(); }
	// 对应 Java: JSONObject properties = deviceShadowEntity.getProperties();
	// if (properties == null) { sb.append(WINDSTRING); return sb.toString(); }
	if len(propertiesMap) == 0 {
		sb.WriteString(WIND_STRING)
		return sb.String()
	}

	// 对应 Java: Integer wind = properties.getInteger(WIND);
	// if (wind == null) { sb.append(WINDSTRING); return sb.toString(); }
	wind := getIntegerProperty(propertiesMap, WIND)
	if wind == nil {
		sb.WriteString(WIND_STRING)
		return sb.String()
	}

	// 对应 Java: switch (wind){ case 0: sb.append(WINDSTRING); break;
	// case 1: sb.append("当前为安静吸力。"); break;
	// case 2: sb.append("当前为中档吸力。"); break;
	// case 3: sb.append("当前为强力吸力。"); break;
	// default: sb.append(WINDSTRING); }
	switch *wind {
	case 0:
		sb.WriteString(WIND_STRING)
	case 1:
		sb.WriteString("当前为安静吸力。")
	case 2:
		sb.WriteString("当前为中档吸力。")
	case 3:
		sb.WriteString("当前为强力吸力。")
	default:
		sb.WriteString(WIND_STRING)
	}

	return sb.String()
}

// getWaterString 获取水量字符串
// 对应 Java: getWaterString(ResponseMessage<DeviceShadowEntity> response)
func (s *bdService) getWaterString(propertiesMap map[string]interface{}) string {
	var sb strings.Builder

	// 对应 Java: 空值检查和默认值处理
	if len(propertiesMap) == 0 {
		sb.WriteString(WATER_STRING)
		return sb.String()
	}

	// 对应 Java: Integer water = properties.getInteger(WATER);
	water := getIntegerProperty(propertiesMap, WATER)
	if water == nil {
		sb.WriteString(WATER_STRING)
		return sb.String()
	}

	// 对应 Java: switch(water条件判断)
	switch *water {
	case 0:
		sb.WriteString(WATER_STRING)
	case 1:
		sb.WriteString("当前为中档水量。")
	case 2:
		sb.WriteString("当前为很湿水量。")
	default:
		sb.WriteString(WATER_STRING)
	}

	return sb.String()
}

// getVolumeString 获取音量字符串
// 对应 Java: getVolumeString(ResponseMessage<DeviceShadowEntity> response)
func (s *bdService) getVolumeString(propertiesMap map[string]interface{}) string {
	var sb strings.Builder

	// 对应 Java: 空值检查和默认值处理
	if len(propertiesMap) == 0 {
		sb.WriteString(VOLUME_STRING)
		return sb.String()
	}

	// 对应 Java: Integer volume = properties.getInteger(VOLUME);
	volume := getIntegerProperty(propertiesMap, VOLUME)
	if volume == nil {
		sb.WriteString(VOLUME_STRING)
		return sb.String()
	}

	// 对应 Java: sb.append("当前音量为"+water);
	sb.WriteString("当前音量为")
	sb.WriteString(strconv.Itoa(*volume))

	return sb.String()
}

// isNotChineseDigitOrAsciiLetter 判断字符类型
// 对应 Java: isNotChineseDigitOrAsciiLetter(char ch) (遵循规则46: 严格按照Java编码内容转换)
func (s *bdService) isNotChineseDigitOrAsciiLetter(ch rune) bool {
	// 对应 Java: if (ch >= '\u4e00' && ch <= '\u9fa5') { return false; }
	if ch >= '\u4e00' && ch <= '\u9fa5' {
		return false
	}
	// 对应 Java: if (Character.isDigit(ch)) { return false; }
	if ch >= '0' && ch <= '9' {
		return false
	}
	// 对应 Java: if (ch >= 'A' && ch <= 'Z') { return false; }
	if ch >= 'A' && ch <= 'Z' {
		return false
	}
	// 对应 Java: if(ch >= 'a' && ch <= 'z'){ return false; }
	if ch >= 'a' && ch <= 'z' {
		return false
	}
	return true
}

// abs 绝对值函数
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}
