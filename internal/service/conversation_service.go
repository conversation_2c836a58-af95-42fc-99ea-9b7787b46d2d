package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/repository"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IConversationService 会话记录服务接口
// 对应 Java: IConversationService (遵循规则2: 完整转换所有使用的mapper方法)
type IConversationService interface {
	// 根据SN删除会话记录 - 对应 Java: void deleteBySn(String sn);
	DeleteBySn(ctx context.Context, sn string) error
	// 分页查询会话记录 - 对应 Java: ResponseMessage<Page<Conversation>> page(ConversationPageReq pageReq) (遵循规则2: 完整转换所有使用的mapper方法)
	Page(ctx context.Context, pageReq *req.ConversationPageReq) webRes.IBaseRes
}

var ConversationService conversationService

type conversationService struct{}

// DeleteBySn 根据SN删除会话记录
// 对应 Java: ConversationServiceImpl.deleteBySn(String sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *conversationService) DeleteBySn(ctx context.Context, sn string) error {
	logger.Infof(ctx, "删除语音会话记录: sn=%s", sn)

	// 对应 Java: LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(Conversation::getSn, sn); conversationMapper.delete(queryWrapper);
	err := repository.ConversationRepository.DeleteBySn(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "删除语音会话记录失败: sn=%s, error=%v", sn, err)
		return err
	}

	logger.Infof(ctx, "删除语音会话记录成功: sn=%s", sn)
	return nil
}

// Page 分页查询会话记录
// 对应 Java: ConversationServiceImpl.page(ConversationPageReq pageReq) (遵循规则2: 完整转换所有使用的mapper方法)
func (s *conversationService) Page(ctx context.Context, pageReq *req.ConversationPageReq) webRes.IBaseRes {
	logger.Infof(ctx, "分页查询会话记录: sn=%s, page=%d, pageSize=%d", pageReq.Sn, pageReq.Page, pageReq.PageSize)

	// 对应 Java: if (ObjectUtils.isEmpty(pageReq) || StringUtils.isBlank(pageReq.getSn())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if pageReq == nil || pageReq.Sn == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: Page<Conversation> conversationPage = conversationMapper.selectPage(new Page<>(pageReq.getPage(), pageReq.getPageSize()), queryWrapper);
	pageResult, err := repository.ConversationRepository.SelectPage(ctx, pageReq.Sn, pageReq.Page, pageReq.PageSize)
	if err != nil {
		logger.Errorfm(ctx, "分页查询会话记录失败: sn=%s, page=%d, pageSize=%d, error=%v", pageReq.Sn, pageReq.Page, pageReq.PageSize, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询会话记录失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(conversationPage);
	logger.Infof(ctx, "分页查询会话记录成功: sn=%s, page=%d, pageSize=%d, total=%d", pageReq.Sn, pageReq.Page, pageReq.PageSize, pageResult.Total)
	return webRes.Cb(pageResult)
}
