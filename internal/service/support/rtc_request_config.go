package support

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/service/remote/req"
)

// RtcRequestConfig RTC请求配置
// 对应 Java: RtcRequestConfig
type RtcRequestConfig struct {
	client *http.Client
}

// 声网API常量
// 对应 Java: RtcRequestConfig 中的常量
const (
	// 请求的域名
	BASE_URL = "https://api.sd-rtn.com/iot/link"
	// 激活设备，获取访问授权令牌
	ACTIVATE_DEVICE = "/open-api/v2/iot-core/secret-node/device/activate"
	// 请求超时时间
	REQUEST_TIMEOUT = 30 * time.Second
)

// NewRtcRequestConfig 创建RTC请求配置实例
func NewRtcRequestConfig() *RtcRequestConfig {
	return &RtcRequestConfig{
		client: &http.Client{
			Timeout: REQUEST_TIMEOUT,
		},
	}
}

// RtcRequestConfig 全局实例
var RtcRequestConfigInstance = NewRtcRequestConfig()

// ActivateDeviceRequest 激活设备，获取访问授权令牌
// 对应 Java: RtcRequestConfig.activateDeviceRequest(RtcDeviceNodeInfoVo rtcDeviceNodeInfoVo)
func (r *RtcRequestConfig) ActivateDeviceRequest(ctx context.Context, nodeInfoVo *req.RtcDeviceNodeInfoVo) (*req.RtcActivateDeviceVo, error) {
	logger.Infof(ctx, "开始调用声网激活设备API: nodeId=%s, appId=%s", nodeInfoVo.NodeId, nodeInfoVo.RtcAppId)

	// 1. 构建请求头 - 对应 Java: headAddAuthorization(rtcDeviceNodeInfoVo)
	headers := r.buildAuthorizationHeader(ctx, nodeInfoVo)

	// 2. 构建请求体 - 对应 Java: request.put(APP_ID, rtcDeviceNodeInfoVo.getRtcAppId())
	requestBody := map[string]interface{}{
		"appId":      nodeInfoVo.RtcAppId,
		"nodeId":     nodeInfoVo.NodeId,
		"nodeSecret": nodeInfoVo.NodeSecret,
		"productKey": nodeInfoVo.RtcProductKey,
	}

	// 3. 序列化请求体
	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		logger.Errorfm(ctx, "序列化请求体失败: error=%v", err)
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 4. 构建HTTP请求
	url := BASE_URL + ACTIVATE_DEVICE
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBodyBytes))
	if err != nil {
		logger.Errorfm(ctx, "创建HTTP请求失败: error=%v", err)
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 5. 设置请求头
	for key, value := range headers {
		httpReq.Header.Set(key, value)
	}

	// 6. 发送请求 - 对应 Java: restTemplate.postForEntity(url, httpEntity, Map.class)
	logger.Infof(ctx, "发送声网API请求: url=%s", url)
	resp, err := r.client.Do(httpReq)
	if err != nil {
		logger.Errorfm(ctx, "发送HTTP请求失败: error=%v", err)
		return nil, fmt.Errorf("网络异常，访问声网失败: %v", err)
	}
	defer resp.Body.Close()

	// 7. 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorfm(ctx, "读取响应体失败: error=%v", err)
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 8. 检查HTTP状态码 - 对应 Java: responseMessage.getStatusCode().isError()
	if resp.StatusCode >= 400 {
		logger.Errorfm(ctx, "声网API返回错误状态码: statusCode=%d, body=%s", resp.StatusCode, string(responseBody))
		return nil, fmt.Errorf("网络异常，访问声网失败，状态码: %d", resp.StatusCode)
	}

	// 9. 解析响应体 - 对应 Java: JSON.toJavaObject(new JSONObject(body), RtcActivateDeviceVo.class)
	var result req.RtcActivateDeviceVo
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		logger.Errorfm(ctx, "解析响应体失败: error=%v, body=%s", err, string(responseBody))
		return nil, fmt.Errorf("解析响应体失败: %v", err)
	}

	logger.Infof(ctx, "声网API调用成功: success=%t, msg=%s", result.Success, result.Msg)
	return &result, nil
}

// buildAuthorizationHeader 构建认证请求头
// 对应 Java: headAddAuthorization(RtcDeviceNodeInfoVo authInfo)
func (r *RtcRequestConfig) buildAuthorizationHeader(ctx context.Context, authInfo *req.RtcDeviceNodeInfoVo) map[string]string {
	// 1. 拼接客户 ID 和客户密钥并使用 base64 编码
	// 对应 Java: String plainCredentials = clientKey + ":" + clientSecret;
	plainCredentials := authInfo.ClientKey + ":" + authInfo.ClientSecret

	// 2. Base64 编码 - 对应 Java: new String(Base64.getEncoder().encode(plainCredentials.getBytes()))
	base64Credentials := base64.StdEncoding.EncodeToString([]byte(plainCredentials))

	// 3. 创建 authorization header - 对应 Java: String authorizationHeader = "Basic " + base64Credentials;
	authorizationHeader := "Basic " + base64Credentials

	// 4. 构建请求头 - 对应 Java: httpHeaders.setContentType(MediaType.APPLICATION_JSON)
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": authorizationHeader,
	}

	logger.Debugf(ctx, "构建认证请求头成功: clientKey=%s", authInfo.ClientKey)
	return headers
}
