package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceMapService 设备地图服务接口
// 对应 Java: IDeviceMapService (遵循规则45: 严格按照Java编码内容转换)
type IDeviceMapService interface {
	// 获取设备当前地图 - 对应 Java: ResponseMessage<DeviceMapEntity> getCurMap(String sn);
	GetCurMap(ctx context.Context, sn string) webRes.IBaseRes
}

var DeviceMapService deviceMapService

type deviceMapService struct{}

// GetCurMap 获取设备当前地图
// 对应 Java: DeviceMapServiceImpl.getCurMap(String sn) (遵循规则45: 严格按照Java编码内容转换)
func (s *deviceMapService) GetCurMap(ctx context.Context, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "获取设备当前地图: sn=%s", sn)

	// 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildSuccess(null); }
	if sn == "" {
		logger.Infof(ctx, "SN为空，返回null: sn=%s", sn)
		return webRes.Cbnd()
	}

	// 对应 Java: LambdaQueryWrapper<DeviceMapEntity> lambda = new QueryWrapper<DeviceMapEntity>().lambda(); lambda.eq(DeviceMapEntity::getSn, sn); List<DeviceMapEntity> deviceMapList = this.list(lambda);
	deviceMapList, err := repository.DeviceMapRepository.FindBySn(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "查询设备地图失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备地图失败")
	}

	// 对应 Java: DeviceMapEntity deviceMapEntity = CollectionUtils.isNotEmpty(deviceMapList) ? deviceMapList.get(0) : null;
	var deviceMapEntity *domain.DeviceMap
	if len(deviceMapList) > 0 {
		deviceMapEntity = deviceMapList[0]
	}

	// 对应 Java: return ResponseMessage.buildSuccess(deviceMapEntity);
	logger.Infof(ctx, "获取设备当前地图成功: sn=%s, found=%v", sn, deviceMapEntity != nil)
	return webRes.Cb(deviceMapEntity)
}
