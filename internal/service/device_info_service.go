package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	webResErr "piceacorp.com/device-service/internal/bean/res/error"
	"piceacorp.com/device-service/internal/service/remote"
	cres "piceacorp.com/device-service/internal/service/remote/res"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	customeTime "piceacorp.com/device-service/pkg/web/common/ptime"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webBaseErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceInfoService 设备信息服务接口
type IDeviceInfoService interface {
	// 根据SN和MAC获取设备信息
	GetBySn(ctx context.Context, sn string) (*domain.DeviceInfo, error)
	// 根据设备ID列表获取设备信息列表
	GetByIds(ctx context.Context, tenantId string, ids []string) ([]*domain.DeviceInfo, error)
	// 修改设备昵称
	ModifyNickname(ctx context.Context, req *req.ModifyDeviceNicknameReq) webRes.IBaseRes
	// 根据设备ID列表获取设备信息（返回ResponseMessage格式）
	GetDeviceInfosByIds(ctx context.Context, deviceIds []string) webRes.IBaseRes
	// 根据设备id获取设备status - 对应 Java: getRobotStatus
	GetRobotStatus(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes
	// 根据SN获取设备state - 对应 Java: getDeviceStatus
	GetDeviceStatus(ctx context.Context, sn string) webRes.IBaseRes
	// 根据SN号获取设备id - 对应 Java: getRobotId
	GetRobotId(ctx context.Context, tenantId, sn string) webRes.IBaseRes
	// 根据SN号获取设备信息 - 对应 Java: getRobotDetail
	GetRobotDetail(ctx context.Context, tenantId, sn string) webRes.IBaseRes
	// 根据设备id查看设备是否激活 - 对应 Java: isActivatedRobot
	IsActivatedRobot(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes
	// 获取所有设备SN列表 - 对应 Java: selectAllRobotSn
	SelectAllRobotSn(ctx context.Context) webRes.IBaseRes
	// 获取产品的设备数量 - 对应 Java: getDeviceCountByPro
	GetDeviceCountByPro(ctx context.Context, productIds []string) webRes.IBaseRes
	// 根据设备id获取设备分类 - 对应 Java: classify
	Classify(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes
	// 根据设备id集合获取设备昵称 - 对应 Java: getDeviceNickNameByIds
	GetDeviceNickNameByIds(ctx context.Context, deviceIds []string) webRes.IBaseRes
	// 根据设备id获取产品型号代码 - 对应 Java: getProductModeCode
	GetProductModeCode(ctx context.Context, deviceId string) webRes.IBaseRes
	// 根据产品型号代码获取设备数量 - 对应 Java: getDeviceCountByProductModeCode
	GetDeviceCountByProductModeCode(ctx context.Context, productModeCode string) webRes.IBaseRes
	// 更新app是否验证密码 - 对应 Java: updateVerifyPassword
	UpdateVerifyPassword(ctx context.Context, req *req.DeviceVerifyPasswordReq) webRes.IBaseRes
	// 根据租户id和设备sn获取设备信息 - 对应 Java: getDeviceSnByTenantId
	GetDeviceSnByTenantId(ctx context.Context, reqList []req.DeviceSnAndTenantReq) webRes.IBaseRes
	// 更新产品下对应的设备的产品图片信息 - 对应 Java: updatePhoto
	UpdatePhoto(ctx context.Context, req *req.UpdatePhotoReq) webRes.IBaseRes
	// 根据设备id获取设备信息 - 对应 Java: getDeviceInfoById
	GetDeviceInfoById(ctx context.Context, id string) webRes.IBaseRes
	// 根据产品型号代码获取活跃设备SN - 对应 Java: getActiveDeviceSNByCode
	GetActiveDeviceSNByCode(ctx context.Context, req *req.DeviceInfoReq) webRes.IBaseRes
	// 根据设备id获取设备信息 - 对应 Java: getById
	GetById(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes
	// 设备配置是否需要开启验证app密码 - 对应 Java: configVerify(DeviceConfigVerifyPasswordVo vo) (遵循规则47: 必须全部转换)
	ConfigVerify(ctx context.Context, req *req.DeviceConfigVerifyPasswordReq) webRes.IBaseRes
	// 更新iotId信息 - 对应 Java: updateIotId(IotIdVo vo) (遵循规则47: 必须全部转换)
	UpdateIotId(ctx context.Context, req *req.IotIdReq) webRes.IBaseRes
	// 通过设备sn获取设备是否在线 - 对应 Java: ifOnline (遵循规则43: 必须全部转换)
	IfOnline(ctx context.Context, sn string) bool
	// 重置设备昵称 - 对应 Java: resetNickname(String sn) (遵循规则43: 必须全部转换)
	ResetNickname(ctx context.Context, sn string) webRes.IBaseRes
	// 根据产品型号代码获取设备数量 - 对应 Java: getCountByCode(String productModelCode) (遵循规则47: 必须全部转换)
	GetCountByCode(ctx context.Context, productModelCode string) (int, error)
	// 根据产品型号代码分页获取设备 - 对应 Java: getPageByCode(String productModelCode, int page, int pageSize) (遵循规则47: 必须全部转换)
	GetPageByCode(ctx context.Context, productModelCode string, page, pageSize int) (*bo.PageResult[*domain.DeviceInfo], error)
	// 根据设备ID列表获取SN列表 - 对应 Java: getSnListByDeviceIds(List<String> deviceIds) (遵循规则47: 必须全部转换)
	GetSnListByDeviceIds(ctx context.Context, deviceIds []string) ([]string, error)
}

var DeviceInfoService deviceInfoService

type deviceInfoService struct{}

// GetBySnAndMac 根据SN和MAC获取设备信息
// 对应 Java: DeviceInfoServiceImpl.getBySnAndMac(String sn, String mac)
func (s *deviceInfoService) GetBySnAndMac(ctx context.Context, sn, mac string) (*domain.DeviceInfo, error) {
	logger.Infof(ctx, "根据SN和MAC获取设备信息: sn=%s, mac=%s", sn, mac)

	// 调用数据访问层查询设备信息
	// 对应 Java: deviceInfoMapper.selectOne(wrapper)
	deviceInfo, err := repository.DeviceRepository.GetBySnAndMac(ctx, sn, mac)
	if err != nil {
		logger.Errorfm(ctx, "查询设备信息失败: sn=%s, mac=%s, error=%v", sn, mac, err)
		return nil, err
	}

	if deviceInfo == nil {
		logger.Warnf(ctx, "设备不存在: sn=%s, mac=%s", sn, mac)
		return nil, nil
	}

	logger.Infof(ctx, "查询设备信息成功: sn=%s, mac=%s, deviceId=%s", sn, mac, deviceInfo.Id)
	return deviceInfo, nil
}

// GetBySn 根据SN获取设备信息
// 对应 Java: 根据DeviceInfoServiceImpl中的查询逻辑实现
func (s *deviceInfoService) GetBySn(ctx context.Context, sn string) (*domain.DeviceInfo, error) {
	logger.Infof(ctx, "根据SN获取设备信息: sn=%s", sn)

	// 调用数据访问层查询设备信息
	deviceInfo, err := repository.DeviceRepository.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "查询设备信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	if deviceInfo == nil {
		logger.Warnf(ctx, "设备不存在: sn=%s", sn)
		return nil, nil
	}

	logger.Infof(ctx, "查询设备信息成功: sn=%s, deviceId=%s", sn, deviceInfo.Id)
	return deviceInfo, nil
}

// GetByIds 根据设备ID列表获取设备信息列表
// 对应 Java: DeviceInfoServiceImpl.getByIds(String tenantId, List<String> ids)
func (s *deviceInfoService) GetByIds(ctx context.Context, tenantId string, ids []string) ([]*domain.DeviceInfo, error) {
	logger.Infof(ctx, "根据设备ID列表获取设备信息: tenantId=%s, count=%d", tenantId, len(ids))

	if len(ids) == 0 {
		logger.Debugf(ctx, "设备ID列表为空，返回空结果")
		return []*domain.DeviceInfo{}, nil
	}

	var devices []*domain.DeviceInfo

	// 循环从缓存获取设备信息 - 对应 Java: for (String id : ids) { deviceInfoCache.getCacheById(tenantId, id) }
	for _, id := range ids {
		deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, id)
		if err != nil {
			logger.Errorfm(ctx, "从缓存获取设备信息失败: deviceId=%s, error=%v", id, err)
			// 缓存获取失败时继续处理下一个，不中断整个流程
			continue
		}

		// 对应 Java: if (Objects.nonNull(deviceInfoEntity))
		if deviceInfo != nil {
			devices = append(devices, deviceInfo)
		} else {
			logger.Warnf(ctx, "设备信息不存在: deviceId=%s", id)
		}
	}

	logger.Infof(ctx, "根据设备ID列表获取设备信息成功: tenantId=%s, requested=%d, found=%d",
		tenantId, len(ids), len(devices))

	return devices, nil
}

// ModifyNickname 修改设备昵称
// 对应 Java: DeviceInfoServiceImpl.modifyNickname(String tenantId, String deviceId, String nickname, String sn, String mac)
func (s *deviceInfoService) ModifyNickname(ctx context.Context, req *req.ModifyDeviceNicknameReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getContextValue(ContextKey.TENANT_ID), SystemContextUtils.getId()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userId := ctx.Value(ctxKeys.ID).(string)

	logger.Infof(ctx, "修改设备昵称开始: tenantId=%s, userId=%s, deviceId=%s, nickname=%s, sn=%s, mac=%s",
		tenantId, userId, req.DeviceId, req.Nickname, req.Sn, req.Mac)

	// 校验设备昵称是否重复 - 对应 Java: if (isNickNameRepeat(userId, deviceId, nickname))
	isRepeat, err := s.isNickNameRepeat(ctx, userId, req.DeviceId, req.Nickname)
	if err != nil {
		logger.Errorfm(ctx, "检查设备昵称重复失败: userId=%s, deviceId=%s, nickname=%s, error=%v",
			userId, req.DeviceId, req.Nickname, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "检查设备昵称重复失败")
	}

	if isRepeat {
		logger.Warnf(ctx, "设备昵称重复: userId=%s, deviceId=%s, nickname=%s", userId, req.DeviceId, req.Nickname)
		return webResErr.DEVICE_SAME_NICKNAME_ERROR
	}

	// 更新设备昵称 - 对应 Java: deviceInfoMapper.update(null, wrapper)
	err = repository.DeviceRepository.UpdateNickname(ctx, "", req.DeviceId, "", req.Nickname, "")
	if err != nil {
		logger.Errorfm(ctx, "更新设备昵称失败: deviceId=%s, nickname=%s, error=%v", req.DeviceId, req.Nickname, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "更新设备昵称失败")
	}

	// 清理设备缓存 - 对应 Java: deviceInfoCache.clear(tenantId, deviceInfo)
	err = cache.DeviceInfoCache.RemoveCache(ctx, tenantId, req.DeviceId)
	if err != nil {
		logger.Errorfm(ctx, "清理设备缓存失败: deviceId=%s, error=%v", req.DeviceId, err)
		// 缓存清理失败不影响主流程，只记录错误
	}

	// 新增语控关系 - 对应 Java: icloudIntentService.insert(userId, sn)
	IcloudIntentService.Insert(ctx, userId, req.Sn)

	logger.Infof(ctx, "修改设备昵称成功: deviceId=%s, nickname=%s", req.DeviceId, req.Nickname)

	// 对应 Java: ResponseMessage.buildSuccess(update > 0)
	return webRes.Cb(true)
}

// GetDeviceInfosByIds 根据设备ID列表获取设备信息（返回ResponseMessage格式）
// 对应 Java: DeviceInfoServiceImpl.getDeviceInfosByIds(List<String> deviceIds)
func (s *deviceInfoService) GetDeviceInfosByIds(ctx context.Context, deviceIds []string) webRes.IBaseRes {
	if len(deviceIds) == 0 {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "ids列表不能为空")
	}

	// 使用 GORM 的 listByIds 方法查询设备信息
	var deviceInfoList []*domain.DeviceInfo
	result := repository.DeviceRepository.Dbh.DB.WithContext(ctx).
		Where("id IN ?", deviceIds).
		Find(&deviceInfoList)

	if result.Error != nil {
		logger.Errorfm(ctx, "根据设备ID列表查询设备信息失败: deviceIds=%v, error=%v", deviceIds, result.Error)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	logger.Debugf(ctx, "根据设备ID列表查询设备信息成功: requested=%d, found=%d", len(deviceIds), len(deviceInfoList))
	return webRes.Cb(deviceInfoList)
}

// isNickNameRepeat 待修改的设备昵称是否与其绑定用户下的设备昵称重复
// 对应 Java: DeviceInfoServiceImpl.isNickNameRepeat(String userId, String deviceId, String nickName)
func (s *deviceInfoService) isNickNameRepeat(ctx context.Context, userId, deviceId, nickName string) (bool, error) {
	// 获取租户ID - 对应 Java: SystemContextUtils.getTenantId()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 获取用户绑定的设备列表 - 对应 Java: deviceBindUserMapper.getBindListByUserIdList(SystemContextUtils.getTenantId(), Collections.singletonList(userId))
	bindIdVoList, err := repository.DeviceBindUserRepository.GetBindListByUserIdList(ctx, tenantId, []string{userId})
	if err != nil {
		logger.Errorfm(ctx, "获取用户绑定设备列表失败: userId=%s, error=%v", userId, err)
		return false, err
	}

	// 过滤掉当前修改的设备 - 对应 Java: .filter(id -> !deviceId.equalsIgnoreCase(id))
	var deviceIds []string
	for _, bindIdVo := range bindIdVoList {
		if bindIdVo.DeviceId != deviceId {
			deviceIds = append(deviceIds, bindIdVo.DeviceId)
		}
	}

	// 如果没有其他设备，则不重复 - 对应 Java: if (CollectionUtils.isEmpty(deviceIds))
	if len(deviceIds) == 0 {
		return false, nil
	}

	// 获取设备信息列表 - 对应 Java: getDeviceInfosByIds(deviceIds)
	deviceInfosResponse := s.GetDeviceInfosByIds(ctx, deviceIds)
	if deviceInfosResponse.Fail() {
		eRes := deviceInfosResponse.(*webRes.ERes)
		logger.Errorfm(ctx, "获取设备信息列表失败: deviceIds=%v, error=%s", deviceIds, eRes)
		return false, fmt.Errorf("获取设备信息列表失败: %s", eRes.Msg)
	}
	eRes := deviceInfosResponse.(*webRes.SRes[[]*domain.DeviceInfo])
	// 类型断言获取设备信息列表
	deviceInfoEntityList := eRes.Result

	// 如果设备信息列表为空，则不重复 - 对应 Java: if (CollectionUtils.isEmpty(deviceInfoEntityList))
	if len(deviceInfoEntityList) == 0 {
		return false, nil
	}

	// 检查昵称是否重复 - 对应 Java: deviceInfoEntityList.stream().anyMatch(t -> nickName.equalsIgnoreCase(t.getNickname()))
	for _, deviceInfo := range deviceInfoEntityList {
		if strings.EqualFold(nickName, deviceInfo.Nickname) {
			logger.Debugf(ctx, "发现重复昵称: userId=%s, deviceId=%s, nickname=%s, conflictDeviceId=%s",
				userId, deviceId, nickName, deviceInfo.Id)
			return true, nil
		}
	}

	return false, nil
}

// GetRobotStatus 根据设备id获取设备status
// 对应 Java: DeviceInnerController.getRobotStatus(String tenantId, String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetRobotStatus(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes {
	logger.Infof(ctx, "获取设备状态: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
	device, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: if (Objects.isNull(device)) { LogUtils.error("设备不存在，tenantId={}, deviceId={}", tenantId, device); return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "设备不存在"); }
	if device == nil {
		logger.Errorfm(ctx, "设备不存在，tenantId=%s, deviceId=%s", tenantId, deviceId)
		return webRes.Ce(webBaseErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: String sn = device.getSn();
	sn := device.Sn

	// 对应 Java: if (!deviceInfoService.ifOnline(sn)) { //离线，则直接返回null return ResponseMessage.buildSuccess(null); }
	if !s.IfOnline(ctx, sn) {
		// 离线，则直接返回null
		return webRes.Cbnd()
	}

	// 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceStatus(sn));
	return s.GetDeviceStatus(ctx, sn)
}

// GetDeviceStatus 根据SN获取设备state
// 对应 Java: DeviceInfoServiceImpl.getDeviceStatus(String sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceStatus(ctx context.Context, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "根据SN获取设备状态: sn=%s", sn)

	// 对应 Java: if (StringUtils.isBlank(sn)) { throw new AppRuntimeException("通过设备sn获取设备状态>> sn不能为空"); }
	if sn == "" {
		logger.Errorfm(ctx, "通过设备sn获取设备状态>> sn不能为空")
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "通过设备sn获取设备状态>> sn不能为空")
	}

	// 对应 Java: ResponseMessage<DeviceShadowEntity> resposne = deviceShadowRemote.findBySN(sn);
	// 对应 Java: if (Objects.isNull(resposne.getResult())) { return null; }
	// 对应 Java: DeviceShadowEntity shadowEntity = resposne.getResult(); return shadowEntity.getProperties();
	shadowEntity, err := remote.DeviceShadowService.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "调用设备影子服务失败: sn=%s, error=%v", sn, err)
		return webRes.Cbnd()
	}

	if shadowEntity == nil {
		return webRes.Cbnd()
	}

	return webRes.Cb(shadowEntity.Properties)
}

// IfOnline 通过设备sn获取设备是否在线
// 对应 Java: DeviceInfoServiceImpl.ifOnline(String sn) (遵循规则43: 必须全部转换)
func (s *deviceInfoService) IfOnline(ctx context.Context, sn string) bool {
	// 对应 Java: if (StringUtils.isBlank(sn)) { throw new AppRuntimeException("通过设备sn获取设备状态>> sn不能为空"); }
	if sn == "" {
		logger.Errorfm(ctx, "通过设备sn获取设备状态>> sn不能为空")
		return false
	}

	// 对应 Java: ResponseMessage<DeviceShadowEntity> resposne = deviceShadowRemote.findBySN(sn);
	// 对应 Java: if (!resposne.isSuccess()) { return false; }
	shadowEntity, err := remote.DeviceShadowService.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "调用设备影子服务失败: sn=%s, error=%v", sn, err)
		return false
	}

	// 对应 Java: DeviceShadowEntity shadowEntity = resposne.getResult(); if (Objects.isNull(shadowEntity)) { return false; }
	if shadowEntity == nil {
		return false
	}

	// 对应 Java: return shadowEntity.getOnlineStatus();
	return shadowEntity.OnlineStatus
}

// GetRobotId 根据SN号获取设备id
// 对应 Java: DeviceInnerController.getRobotId(String tenantId, String sn)
func (s *deviceInfoService) GetRobotId(ctx context.Context, tenantId, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "根据SN获取设备ID: tenantId=%s, sn=%s", tenantId, sn)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn);
	device, err := cache.DeviceInfoCache.GetCacheBySn(ctx, sn, tenantId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(Objects.isNull(device) ? null : device.getId());
	if device == nil {
		return webRes.Cbnd()
	}

	return webRes.Cb(device.Id)
}

// GetRobotDetail 根据SN号获取设备信息
// 对应 Java: DeviceInnerController.getRobotDetail(String tenantId, String sn)
func (s *deviceInfoService) GetRobotDetail(ctx context.Context, tenantId, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "根据SN获取设备详情: tenantId=%s, sn=%s", tenantId, sn)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn);
	device, err := cache.DeviceInfoCache.GetCacheBySn(ctx, sn, tenantId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(device);
	return webRes.Cb(device)
}

// IsActivatedRobot 根据设备id查看设备是否激活
// 对应 Java: DeviceInnerController.isActivatedRobot(String tenantId, String deviceId)
func (s *deviceInfoService) IsActivatedRobot(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes {
	logger.Infof(ctx, "查看设备是否激活: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
	device, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(device != null);
	return webRes.Cb(device != nil)
}

// SelectAllRobotSn 获取设备所有sn列表
// 对应 Java: DeviceInfoServiceImpl.selectAllRobotSn() (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) SelectAllRobotSn(ctx context.Context) webRes.IBaseRes {
	logger.Infof(ctx, "获取设备所有sn列表")

	// 对应 Java: QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>(); wrapper.select("sn");
	// 对应 Java: List<DeviceInfoEntity> deviceInfoEntities = deviceInfoMapper.selectList(wrapper);
	deviceInfoEntities, err := repository.DeviceRepository.SelectAllSn(ctx)
	if err != nil {
		logger.Errorfm(ctx, "查询设备SN列表失败: error=%v", err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备SN列表失败")
	}

	// 对应 Java: if (CollectionUtils.isEmpty(deviceInfoEntities)) { return new ArrayList<>(); }
	if len(deviceInfoEntities) == 0 {
		return webRes.Cb([]string{})
	}

	// 对应 Java: return deviceInfoEntities.stream().map(m -> m.getSn()).collect(Collectors.toList());
	snList := make([]string, len(deviceInfoEntities))
	for i, device := range deviceInfoEntities {
		snList[i] = device.Sn
	}

	logger.Infof(ctx, "获取设备所有sn列表成功: count=%d", len(snList))
	return webRes.Cb(snList)
}

// GetDeviceCountByPro 通过产品id列表获取设备数量
// 对应 Java: DeviceInfoServiceImpl.getDeviceCountByPro(List<String> productIds) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceCountByPro(ctx context.Context, productIds []string) webRes.IBaseRes {
	logger.Infof(ctx, "获取产品设备数量: productIds=%v", productIds)

	// 对应 Java: Map<String, Long> map = new HashMap<>();
	// 对应 Java: List<Map<String, Long>> res = baseMapper.selectCountByProuctId(productIds);
	countMap, err := repository.DeviceRepository.SelectCountByProductId(ctx, productIds)
	if err != nil {
		logger.Errorfm(ctx, "查询产品设备数量失败: productIds=%v, error=%v", productIds, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询产品设备数量失败")
	}

	logger.Infof(ctx, "获取产品设备数量成功: productIds=%v, result=%v", productIds, countMap)
	return webRes.Cb(countMap)
}

// Classify 根据设备id获取设备分类
// 对应 Java: DeviceInnerController.classify(String tenantId, String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) Classify(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes {
	logger.Infof(ctx, "获取设备分类: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
	device, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	if device == nil {
		logger.Errorfm(ctx, "设备不存在: deviceId=%s", deviceId)
		return webRes.Ce(webBaseErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: return productServiceRemote.getProductClassify(device.getProductId());
	result := remote.ProductService.GetProductClassify(ctx, device.ProductId)
	if result == nil || result.Fail() {
		logger.Errorfm(ctx, "调用产品服务获取分类失败: productId=%s, result=%+v", device.ProductId, result)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取产品分类失败")
	}

	logger.Infof(ctx, "获取设备分类成功: tenantId=%s, deviceId=%s, productId=%s", tenantId, deviceId, device.ProductId)
	return webRes.Cb(result.Result)
}

// GetDeviceNickNameByIds 根据设备id集合获取设备昵称
// 对应 Java: DeviceInfoServiceImpl.getDeviceNickNameByIds(List<String> ids) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceNickNameByIds(ctx context.Context, deviceIds []string) webRes.IBaseRes {
	logger.Infof(ctx, "根据设备ID集合获取设备昵称: deviceIds=%v", deviceIds)

	// 对应 Java: if (CollectionUtil.isEmpty(ids)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "ids列表不能为空"); }
	if len(deviceIds) == 0 {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "ids列表不能为空")
	}

	// 对应 Java: List<DeviceInfoEntity> deviceInfoEntityList = this.listByIds(ids);
	deviceInfoEntityList, err := repository.DeviceRepository.FindByIds(ctx, deviceIds)
	if err != nil {
		logger.Errorfm(ctx, "根据ID列表查询设备信息失败: deviceIds=%v, error=%v", deviceIds, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	// 对应 Java: Map<String, String> nickNameMap = null;
	var nickNameMap map[string]string

	// 对应 Java: if (CollectionUtil.isNotEmpty(deviceInfoEntityList)) { nickNameMap = deviceInfoEntityList.stream().collect(Collectors.toMap(DeviceInfoEntity::getId, DeviceInfoEntity::getNickname)); }
	if len(deviceInfoEntityList) > 0 {
		nickNameMap = make(map[string]string)
		for _, device := range deviceInfoEntityList {
			nickNameMap[device.Id] = device.Nickname
		}
	}

	// 对应 Java: return ResponseMessage.buildSuccess(nickNameMap);
	logger.Infof(ctx, "根据设备ID集合获取设备昵称成功: deviceIds=%v, count=%d", deviceIds, len(nickNameMap))
	return webRes.Cb(nickNameMap)
}

// GetProductModeCode 根据设备id获取产品型号代码
// 对应 Java: DeviceInfoServiceImpl.getProductModeCode(String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetProductModeCode(ctx context.Context, deviceId string) webRes.IBaseRes {
	logger.Infof(ctx, "根据设备ID获取产品型号代码: deviceId=%s", deviceId)

	// 对应 Java: DeviceInfoEntity device = this.getById(deviceId);
	device, err := repository.DeviceRepository.FindById(ctx, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "根据设备ID查询设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	// 对应 Java: return device != null ? device.getProductModeCode() : null;
	if device == nil {
		return webRes.Cbnd()
	}

	logger.Infof(ctx, "根据设备ID获取产品型号代码成功: deviceId=%s, productModeCode=%s", deviceId, device.ProductModeCode)
	return webRes.Cb(device.ProductModeCode)
}

// GetDeviceCountByProductModeCode 根据产品型号代码获取设备数量
// 对应 Java: DeviceInfoServiceImpl.getCountByCode(String productModeCode) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceCountByProductModeCode(ctx context.Context, productModeCode string) webRes.IBaseRes {
	logger.Infof(ctx, "根据产品型号代码获取设备数量: productModeCode=%s", productModeCode)

	// 对应 Java: QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(DeviceInfoEntity::getProductModeCode, productModeCode);
	// 对应 Java: return deviceInfoMapper.selectCount(wrapper).intValue();
	count, err := repository.DeviceRepository.CountByProductModeCode(ctx, productModeCode)
	if err != nil {
		logger.Errorfm(ctx, "根据产品型号代码查询设备数量失败: productModeCode=%s, error=%v", productModeCode, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备数量失败")
	}

	logger.Infof(ctx, "根据产品型号代码获取设备数量成功: productModeCode=%s, count=%d", productModeCode, count)
	return webRes.Cb(count)
}

// UpdateVerifyPassword 更新app是否验证密码
// 对应 Java: DeviceInfoServiceImpl.updateVerifyPassword(DeviceVerifyPasswordVo vo) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) UpdateVerifyPassword(ctx context.Context, req *req.DeviceVerifyPasswordReq) webRes.IBaseRes {
	logger.Infof(ctx, "更新app验证密码开始: verifyPassword=%d, deviceIds=%v", req.VerifyPassword, req.DeviceIds)

	// 对应 Java: if (Objects.isNull(vo)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if req == nil {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: if (CollectionUtils.isEmpty(vo.getDeviceIds())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if len(req.DeviceIds) == 0 {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>(); wrapper.lambda().set(DeviceInfoEntity::getVerifyPassword, vo.getVerifyPassword()).in(DeviceInfoEntity::getId, vo.getDeviceIds());
	// 对应 Java: int update = deviceInfoMapper.update(null, wrapper);
	rowsAffected, err := repository.DeviceRepository.UpdateVerifyPassword(ctx, req.DeviceIds, req.VerifyPassword)
	if err != nil {
		logger.Errorfm(ctx, "更新app验证密码失败: verifyPassword=%d, deviceIds=%v, error=%v", req.VerifyPassword, req.DeviceIds, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "更新app验证密码失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(update > 0);
	result := rowsAffected > 0
	logger.Infof(ctx, "更新app验证密码完成: verifyPassword=%d, deviceIds=%v, rowsAffected=%d, result=%v",
		req.VerifyPassword, req.DeviceIds, rowsAffected, result)
	return webRes.Cb(result)
}

// GetDeviceSnByTenantId 根据租户id和设备sn获取设备信息
// 对应 Java: DeviceInfoServiceImpl.getDeviceSnByTenantId(List<DeviceSnAndTenantReq> reqList) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceSnByTenantId(ctx context.Context, reqList []req.DeviceSnAndTenantReq) webRes.IBaseRes {
	logger.Infof(ctx, "根据租户ID和设备SN获取设备信息: count=%d", len(reqList))

	// 对应 Java: if (CollectionUtils.isEmpty(reqList)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if len(reqList) == 0 {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: List<DeviceInfoEntity> deviceInfoEntityList = new ArrayList<>();
	var deviceInfoEntityList []*domain.DeviceInfo

	// 对应 Java: for (DeviceSnAndTenantReq req : reqList) { DeviceInfoEntity device = deviceInfoCache.getCacheBySn(req.getTenantId(), req.getSn()); if (Objects.nonNull(device)) { deviceInfoEntityList.add(device); } }
	for _, reqItem := range reqList {
		device, err := cache.DeviceInfoCache.GetCacheBySn(ctx, reqItem.Sn, reqItem.TenantId)
		if err != nil {
			logger.Errorfm(ctx, "获取设备缓存失败: sn=%s, tenantId=%s, error=%v", reqItem.Sn, reqItem.TenantId, err)
			continue
		}
		if device != nil {
			deviceInfoEntityList = append(deviceInfoEntityList, device)
		}
	}

	// 对应 Java: return ResponseMessage.buildSuccess(deviceInfoEntityList);
	logger.Infof(ctx, "根据租户ID和设备SN获取设备信息成功: requestCount=%d, resultCount=%d", len(reqList), len(deviceInfoEntityList))
	return webRes.Cb(deviceInfoEntityList)
}

// UpdatePhoto 更新产品下对应的设备的产品图片信息
// 对应 Java: DeviceInfoServiceImpl.updatePhoto(UpdatePhotoVo vo) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) UpdatePhoto(ctx context.Context, req *req.UpdatePhotoReq) webRes.IBaseRes {
	logger.Infof(ctx, "更新产品图片开始: productId=%s, photoUrl=%s, tenantId=%s", req.ProductId, req.PhotoUrl, req.TenantId)

	// 对应 Java: if (Objects.isNull(vo)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if req == nil {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>(); wrapper.lambda().set(DeviceInfoEntity::getPhotoUrl, vo.getPhotoUrl()).eq(DeviceInfoEntity::getProductId, vo.getProductId()).eq(DeviceInfoEntity::getTenantId, vo.getTenantId());
	// 对应 Java: int update = deviceInfoMapper.update(null, wrapper);
	rowsAffected, err := repository.DeviceRepository.UpdatePhotoByProductId(ctx, req.ProductId, req.TenantId, req.PhotoUrl)
	if err != nil {
		logger.Errorfm(ctx, "更新产品图片失败: productId=%s, photoUrl=%s, tenantId=%s, error=%v", req.ProductId, req.PhotoUrl, req.TenantId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "更新产品图片失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(update > 0);
	result := rowsAffected > 0
	logger.Infof(ctx, "更新产品图片完成: productId=%s, photoUrl=%s, tenantId=%s, rowsAffected=%d, result=%v",
		req.ProductId, req.PhotoUrl, req.TenantId, rowsAffected, result)
	return webRes.Cb(result)
}

// GetDeviceInfoById 根据设备id获取设备信息
// 对应 Java: DeviceInfoServiceImpl.getDeviceInfoById(String id) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetDeviceInfoById(ctx context.Context, id string) webRes.IBaseRes {
	logger.Infof(ctx, "根据设备ID获取设备信息: id=%s", id)

	// 对应 Java: DeviceInfoEntity device = this.getById(id);
	device, err := repository.DeviceRepository.FindById(ctx, id)
	if err != nil {
		logger.Errorfm(ctx, "根据设备ID查询设备信息失败: id=%s, error=%v", id, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(device);
	logger.Infof(ctx, "根据设备ID获取设备信息成功: id=%s, found=%v", id, device != nil)
	return webRes.Cb(device)
}

// GetActiveDeviceSNByCode 根据产品型号代码获取活跃设备SN
// 对应 Java: DeviceInfoServiceImpl.getActiveDeviceSNByCode(DeviceInfoReq deviceInfoReq) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetActiveDeviceSNByCode(ctx context.Context, req *req.DeviceInfoReq) webRes.IBaseRes {
	logger.Infof(ctx, "根据产品型号代码获取活跃设备SN: tenantId=%s, productModeCode=%s, percent=%d",
		req.TenantId, req.ProductModeCode, req.Percent)

	// 对应 Java: TenantIdHandleUtil.handleTenantId(deviceInfoReq.getTenantId(), false);
	// 这里暂时跳过租户ID处理，因为Go版本可能有不同的实现

	// 对应 Java: Long total = this.baseMapper.getCountByCode(deviceInfoReq.getProductModeCode(), deviceInfoReq.getTenantId());
	total, err := repository.DeviceRepository.GetCountByCodeAndTenant(ctx, req.ProductModeCode, req.TenantId)
	if err != nil {
		logger.Errorfm(ctx, "查询产品型号代码设备总数失败: productModeCode=%s, tenantId=%s, error=%v", req.ProductModeCode, req.TenantId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询设备总数失败")
	}

	// 对应 Java: int queryCount = total.intValue() * deviceInfoReq.getPercent() / 100;
	queryCount := int(total) * req.Percent / 100

	// 对应 Java: LambdaQueryWrapper<DeviceInfoEntity> queryWrapper = new LambdaQueryWrapper(); queryWrapper.select(DeviceInfoEntity::getSn); queryWrapper.eq(DeviceInfoEntity::getProductModeCode, deviceInfoReq.getProductModeCode()).eq(DeviceInfoEntity::getTenantId, deviceInfoReq.getTenantId()); queryWrapper.orderByDesc(DeviceInfoEntity::getOnlineTime); queryWrapper.last("limit " + queryCount);
	snList, err := repository.DeviceRepository.GetActiveDeviceSNByCode(ctx, req.ProductModeCode, req.TenantId, queryCount)
	if err != nil {
		logger.Errorfm(ctx, "查询活跃设备SN失败: productModeCode=%s, tenantId=%s, queryCount=%d, error=%v", req.ProductModeCode, req.TenantId, queryCount, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "查询活跃设备SN失败")
	}

	// 对应 Java: return deviceInfoEntities.stream().map(DeviceInfoEntity::getSn).collect(Collectors.toList());
	logger.Infof(ctx, "根据产品型号代码获取活跃设备SN成功: productModeCode=%s, tenantId=%s, total=%d, queryCount=%d, resultCount=%d",
		req.ProductModeCode, req.TenantId, total, queryCount, len(snList))
	return webRes.Cb(snList)
}

// GetById 根据设备id获取设备信息
// 对应 Java: DeviceInnerController.getById(String tenantId, String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetById(ctx context.Context, tenantId, deviceId string) webRes.IBaseRes {
	logger.Infof(ctx, "根据设备ID获取设备信息: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
	device, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(device);
	logger.Infof(ctx, "根据设备ID获取设备信息成功: tenantId=%s, deviceId=%s, found=%v", tenantId, deviceId, device != nil)
	return webRes.Cb(device)
}

// ResetNickname 重置设备昵称
// 对应 Java: DeviceInfoServiceImpl.resetNickname(String sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInfoService) ResetNickname(ctx context.Context, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "重置设备昵称开始: sn=%s", sn)

	// 对应 Java: String tenantId = SystemContextUtils.getTenantId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
	deviceInfo, err := cache.DeviceInfoCache.GetCacheBySn(ctx, sn, tenantId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	if deviceInfo == nil {
		logger.Errorfm(ctx, "设备不存在: sn=%s", sn)
		return webRes.Ce(webBaseErr.NOT_FOUND_DATA, "设备不存在")
	}

	logger.Infof(ctx, "resetNickname-deviceInfo: id=%s, sn=%s, defaultNickname=%s", deviceInfo.Id, deviceInfo.Sn, deviceInfo.DefaultNickname)

	// 对应 Java: UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>(); LambdaUpdateWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
	// 对应 Java: lambda.eq(DeviceInfoEntity::getSn, sn).eq(DeviceInfoEntity::getTenantId, tenantId).set(DeviceInfoEntity::getNickname, deviceInfo.getDefaultNickname()).set(DeviceInfoEntity::getDefaultNickname, deviceInfo.getDefaultNickname());
	// 对应 Java: int update = deviceInfoMapper.update(null, wrapper);
	rowsAffected, err := repository.DeviceRepository.ResetNickname(ctx, sn, tenantId, deviceInfo.DefaultNickname)
	if err != nil {
		logger.Errorfm(ctx, "重置设备昵称失败: sn=%s, tenantId=%s, defaultNickname=%s, error=%v", sn, tenantId, deviceInfo.DefaultNickname, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "重置设备昵称失败")
	}

	// 对应 Java: deviceInfoCache.clear(tenantId, deviceInfo);
	err = cache.DeviceInfoCache.Clear(ctx, tenantId, deviceInfo)
	if err != nil {
		logger.Errorfm(ctx, "清除设备缓存失败: sn=%s, tenantId=%s, error=%v", sn, tenantId, err)
		// 缓存清理失败不影响主流程，只记录错误
	}

	// 对应 Java: return ResponseMessage.buildSuccess(update > 0);
	result := rowsAffected > 0
	logger.Infof(ctx, "重置设备昵称完成: sn=%s, tenantId=%s, defaultNickname=%s, rowsAffected=%d, result=%v",
		sn, tenantId, deviceInfo.DefaultNickname, rowsAffected, result)
	return webRes.Cb(result)
}

// handleDeviceInfoStatus 更新设备状态信息
// 对应 Java: DeviceInfoServiceImpl.handleDeviceInfoStatus(List<DeviceInfoEntity> devices)
func (s *deviceInfoService) handleDeviceInfoStatus(ctx context.Context, devices []*domain.DeviceInfo) {
	if len(devices) == 0 {
		return
	}

	// 收集所有的SN列表 - 对应 Java: List<String> sns = devices.stream().map(m -> m.getSn()).distinct().collect(Collectors.toList());
	var sns []string
	snMap := make(map[string]bool)
	for _, device := range devices {
		if !snMap[device.Sn] {
			sns = append(sns, device.Sn)
			snMap[device.Sn] = true
		}
	}

	// 查询所有设备影子信息 - 对应 Java: ResponseMessage<List<DeviceShadowEntity>> responseMessage = deviceShadowRemote.findAllBySN(sns);
	responseMessage := remote.DeviceShadowService.FindAllBySN(ctx, sns)
	if responseMessage == nil || responseMessage.Fail() {
		logger.Errorfm(ctx, "批量查询设备影子失败: sns=%v, response=%+v", sns, responseMessage)
		return
	}

	shadowEntities := *responseMessage.Result
	if len(shadowEntities) == 0 {
		logger.Warnf(ctx, "没有找到设备影子信息: sns=%v", sns)
		return
	}

	// 构建SN与在线状态的映射 - 对应 Java: Map<String, Boolean> snAndStatus = result.stream().filter(n -> Objects.nonNull(n.getOnlineStatus())).collect(Collectors.toMap(n -> n.getId(), m -> m.getOnlineStatus(), (o, n) -> n));
	snAndStatus := make(map[string]bool)
	for _, shadow := range shadowEntities {
		snAndStatus[shadow.Id] = shadow.OnlineStatus
	}

	// 构建SN与时间戳的映射 - 对应 Java: Map<String, Long> collect = result.stream().filter(t -> Objects.nonNull(t.getTimestamp())).collect(Collectors.toMap(DeviceShadowEntity::getId, v -> v.getTimestamp(), (o, n) -> n));
	snAndTimestamp := make(map[string]customeTime.Ptime)
	for _, shadow := range shadowEntities {
		if !shadow.Timestamp.Time().IsZero() {
			snAndTimestamp[shadow.Id] = shadow.Timestamp
		}
	}

	// 更新设备状态 - 对应 Java: devices.forEach((DeviceInfoEntity device) -> { if (snAndStatus.containsKey(device.getSn())) { device.setStatus(snAndStatus.get(device.getSn())); } });
	for _, device := range devices {
		if status, ok := snAndStatus[device.Sn]; ok {
			device.Status = status
		}
	}

	// 更新设备时间戳 - 对应 Java: devices.forEach((DeviceInfoEntity device) -> { if (collect.containsKey(device.getSn())) { device.setTimeStamp(collect.get(device.getSn())); } });
	for _, device := range devices {
		if timestamp, ok := snAndTimestamp[device.Sn]; ok {
			device.TimeStamp = timestamp
		}
	}

	logger.Infof(ctx, "批量更新设备状态完成: deviceCount=%d, shadowCount=%d", len(devices), len(shadowEntities))
}

// genNickName 生成设备昵称
// 对应 Java: DeviceInfoServiceImpl.genNickName(String sn, ProductModeEntity productModeEntity)
func (s *deviceInfoService) genNickName(sn string, productMode *cres.ProductModeEntity) string {
	// 获取别名前缀和后缀位数
	aliasPrefix := productMode.AliasPrefix
	snSuffixBit := productMode.SnSuffixBit
	snLength := len(sn)

	// 计算后缀起始位置
	var suffix string
	if snLength <= *snSuffixBit {
		suffix = sn
	} else {
		suffix = sn[snLength-*snSuffixBit:]
	}

	return aliasPrefix + suffix
}

// GetCountByCode 根据产品型号代码获取设备数量
// 对应 Java: DeviceInfoServiceImpl.getCountByCode(String productModelCode) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetCountByCode(ctx context.Context, productModelCode string) (int, error) {
	logger.Infof(ctx, "根据产品型号代码获取设备数量: productModelCode=%s", productModelCode)

	// 对应 Java: QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(DeviceInfoEntity::getProductModeCode, productModelCode); return deviceInfoMapper.selectCount(wrapper).intValue();
	count, err := repository.DeviceRepository.CountByProductModeCode(ctx, productModelCode)
	if err != nil {
		logger.Errorfm(ctx, "根据产品型号代码查询设备数量失败: productModelCode=%s, error=%v", productModelCode, err)
		return 0, err
	}

	logger.Infof(ctx, "根据产品型号代码获取设备数量成功: productModelCode=%s, count=%d", productModelCode, count)
	return int(count), nil
}

// GetPageByCode 根据产品型号代码分页获取设备
// 对应 Java: DeviceInfoServiceImpl.getPageByCode(String productModelCode, int page, int pageSize) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetPageByCode(ctx context.Context, productModelCode string, page, pageSize int) (*bo.PageResult[*domain.DeviceInfo], error) {
	logger.Infof(ctx, "根据产品型号代码分页获取设备: productModelCode=%s, page=%d, pageSize=%d", productModelCode, page, pageSize)

	// 对应 Java: Page<DeviceInfoEntity> pageResult = this.page(new Page<>(page, pageSize), wrapper);
	pageResult, err := repository.DeviceRepository.GetPageByProductModeCode(ctx, productModelCode, page, pageSize)
	if err != nil {
		logger.Errorfm(ctx, "根据产品型号代码分页查询设备失败: productModelCode=%s, page=%d, pageSize=%d, error=%v",
			productModelCode, page, pageSize, err)
		return nil, err
	}

	logger.Infof(ctx, "根据产品型号代码分页获取设备成功: productModelCode=%s, page=%d, pageSize=%d, total=%d",
		productModelCode, page, pageSize, pageResult.Total)
	return pageResult, nil
}

// GetSnListByDeviceIds 根据设备ID列表获取SN列表
// 对应 Java: DeviceInfoServiceImpl.getSnListByDeviceIds(List<String> deviceIds) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInfoService) GetSnListByDeviceIds(ctx context.Context, deviceIds []string) ([]string, error) {
	logger.Infof(ctx, "根据设备ID列表获取SN列表: deviceIds=%v", deviceIds)

	// 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return new ArrayList<>(); }
	if len(deviceIds) == 0 {
		return []string{}, nil
	}

	// 对应 Java: LambdaQueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<DeviceInfoEntity>().lambda(); wrapper.in(DeviceInfoEntity::getId, deviceIds); List<DeviceInfoEntity> deviceList = this.list(wrapper);
	deviceList, err := repository.DeviceRepository.GetByDeviceIds(ctx, deviceIds)
	if err != nil {
		logger.Errorfm(ctx, "根据设备ID列表查询设备失败: deviceIds=%v, error=%v", deviceIds, err)
		return nil, err
	}

	// 对应 Java: return deviceList.stream().map(DeviceInfoEntity::getSn).collect(Collectors.toList());
	var snList []string
	for _, device := range deviceList {
		snList = append(snList, device.Sn)
	}

	logger.Infof(ctx, "根据设备ID列表获取SN列表成功: deviceIds=%v, snList=%v", deviceIds, snList)
	return snList, nil
}

// ConfigVerify 设备配置是否需要开启验证app密码
// 对应 Java: DeviceInfoServiceImpl.configVerify(DeviceConfigVerifyPasswordVo vo) (遵循规则47: 严格按照Java编码内容转换)
func (s *deviceInfoService) ConfigVerify(ctx context.Context, req *req.DeviceConfigVerifyPasswordReq) webRes.IBaseRes {
	logger.Infof(ctx, "设备配置是否需要开启验证app密码: deviceId=%s, verify=%d", req.DeviceId, req.Verify)

	// 对应 Java: String deviceId = vo.getDeviceId(); Integer verify = vo.getVerify();
	deviceId := req.DeviceId
	verify := req.Verify

	// 对应 Java: String tenantId = SystemContextUtils.getTenantId(); String userId = SystemContextUtils.getId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userId := ctx.Value(ctxKeys.ID).(string)

	// 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); }
	if deviceId == "" {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	// 对应 Java: if (Objects.isNull(verify)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "verify"); }
	// 这里Go中已经通过validator验证了，不需要再次验证

	// 对应 Java: if (!DeviceVerifyEmum.check(verify)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "verify 非法"); }
	if verify != 0 && verify != 1 { // 对应 Java: DeviceVerifyEmum.CLOSE.getCode() = 0, DeviceVerifyEmum.OPEN.getCode() = 1
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "verify 非法")
	}

	// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil || deviceInfo == nil {
		logger.Errorfm(ctx, "设备不存在: deviceId=%s, tenantId=%s, error=%v", deviceId, tenantId, err)
		return webRes.Ce(webBaseErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: if (Objects.isNull(deviceInfo)) { return ResponseMessage.buildFail(NOT_FOUND_DATA, "设备不存在"); }
	// 上面已经处理了

	// 对应 Java: DeviceInfoEntity updateEntity = new DeviceInfoEntity(); updateEntity.setId(deviceId); updateEntity.setVerifyPassword(verify); updateEntity.setUpdateBy(userId); updateEntity.setUpdateTime(LocalDateTime.now());
	updateEntity := &domain.DeviceInfo{
		Id:             deviceId,
		VerifyPassword: verify,
		UpdateBy:       userId,
		UpdateTime:     customeTime.Ptime(time.Now()),
	}

	// 对应 Java: this.updateById(updateEntity);
	err = repository.DeviceRepository.UpdateById(ctx, updateEntity)
	if err != nil {
		logger.Errorfm(ctx, "更新设备验证密码配置失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "更新设备验证密码配置失败")
	}

	// 对应 Java: deviceInfoCache.clear(tenantId, deviceId);
	cache.DeviceInfoCache.Clear(ctx, tenantId, deviceInfo)

	logger.Infof(ctx, "设备配置是否需要开启验证app密码成功: deviceId=%s, verify=%d", deviceId, verify)
	return webRes.Cbnd()
}

// UpdateIotId 更新iotId信息
// 对应 Java: DeviceInfoServiceImpl.updateIotId(IotIdVo vo) (遵循规则47: 严格按照Java编码内容转换)
func (s *deviceInfoService) UpdateIotId(ctx context.Context, req *req.IotIdReq) webRes.IBaseRes {
	logger.Infof(ctx, "更新iotId信息: deviceId=%s, iotId=%s", req.DeviceId, string(req.IotId))

	// 对应 Java: if (StringUtils.isBlank(vo.getDeviceId())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, DEVICE_ID); }
	if req.DeviceId == "" {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	// 对应 Java: if (Objects.isNull(vo.getIotId())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "iotId"); }
	if len(req.IotId) == 0 {
		return webRes.Ce(webBaseErr.PARAM_VALIDATE_FAIL, "iotId")
	}

	// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(SystemContextUtils.getTenantId(), vo.getDeviceId());
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, req.DeviceId)
	if err != nil || deviceInfo == nil {
		logger.Errorfm(ctx, "设备不存在: deviceId=%s, tenantId=%s, error=%v", req.DeviceId, tenantId, err)
		return webRes.Ce(webBaseErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: if (Objects.isNull(deviceInfo)) { return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "设备不存在"); }
	// 上面已经处理了

	// 对应 Java: String iotId = vo.getIotId().toJSONString();
	iotId := string(req.IotId)

	// 对应 Java: return ResponseMessage.buildSuccess(deviceInfoMapper.updateIotId(vo.getDeviceId(), iotId) > 0);
	updateCount, err := repository.DeviceRepository.UpdateIotId(ctx, req.DeviceId, iotId)
	if err != nil {
		logger.Errorfm(ctx, "更新设备iotId失败: deviceId=%s, error=%v", req.DeviceId, err)
		return webRes.Ce(webBaseErr.INNER_SERVER_ERROR, "更新设备iotId失败")
	}

	result := updateCount > 0
	logger.Infof(ctx, "更新iotId信息成功: deviceId=%s, updateCount=%d, result=%v", req.DeviceId, updateCount, result)
	return webRes.Cb(result)
}
