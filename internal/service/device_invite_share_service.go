package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	remoteRes "piceacorp.com/device-service/internal/service/remote/res"
	"piceacorp.com/device-service/pkg/logger"
	putil "piceacorp.com/device-service/pkg/util"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceInviteShareService 设备分享服务接口
type IDeviceInviteShareService interface {
	// 获取分享历史
	GetShareHis(ctx context.Context, req *req.UserSharedHisReq) webRes.IBaseRes
	// 邀请用户分享设备
	InviteUserShareRobot(ctx context.Context, shareReq *req.ShareReq) webRes.IBaseRes
	// 回应用户的分享
	ReplyShared(ctx context.Context, replyReq *req.ShareReplyReq) webRes.IBaseRes
	// 根据被邀请者删除分享记录
	DelByInvitee(ctx context.Context, beInviteeId, targetId string, shareType int)
	// 取消分享
	CancelShare(ctx context.Context, shareId, userId string) bool
	// 获取用户的分享设备信息 - 对应 Java: getShareRobot(GetUserSharedReq req) (遵循规则47: 必须全部转换)
	GetShareRobot(ctx context.Context, req *req.GetUserSharedReq) webRes.IBaseRes
	// 通过极光推送的消息拒绝或同意分享 - 对应 Java: changeShareRecordStatus(ShareRecordChangeStatusReq req) (遵循规则47: 必须全部转换)
	ChangeShareRecordStatus(ctx context.Context, req *req.ShareRecordChangeStatusReq) webRes.IBaseRes
	// 根据设备ID删除分享记录
	DelByDeviceId(ctx context.Context, deviceId string) bool
	// 删除分享记录 - 对应 Java: delShare(DelShareReq req) (遵循规则43: 必须全部转换)
	DelShare(ctx context.Context, req *req.DelShareReq) webRes.IBaseRes
	// 分享家庭 - 对应 Java: doShareFamily(ShareFamilyVo vo) (遵循规则47: 必须全部转换)
	DoShareFamily(ctx context.Context, vo *req.ShareFamilyReq) webRes.IBaseRes
	// 根据用户ID删除分享记录 - 对应 Java: delByUserId(String tenantId, String userId) (遵循规则47: 必须全部转换)
	DelByUserId(ctx context.Context, tenantId, userId string) bool
}

var DeviceInviteShareService deviceInviteShareService

type deviceInviteShareService struct{}

// GetShareHis 获取分享历史
// 对应 Java: DeviceInviteShareServiceImpl.getShareHis(UserSharedHisReq req)
func (s *deviceInviteShareService) GetShareHis(ctx context.Context, req *req.UserSharedHisReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: String userId = SystemContextUtils.getId(); String tenantId = SystemContextUtils.getTenantId();
	userId := ctx.Value(ctxKeys.ID).(string)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	logger.Infof("获取分享历史开始: userId=%s, tenantId=%s, targetId=%s, type=%d, page=%d, pageSize=%d",
		userId, tenantId, req.TargetId, req.Type, req.Page, req.PageSize)

	logger.Infof("getShareHisTDL,获取用户的分享历史")

	// 1. 分页查询分享历史记录 - 对应 Java: Page<DeviceShareHisVo> hisPage = deviceInviteShareMapper.getDeviceShareHis(new Page<>(req.getPage(), req.getPageSize()), userId, req.getTargetId(), req.getType());
	hisPage, err := repository.DeviceInviteShareRepository.GetDeviceShareHis(ctx, userId, req.TargetId, req.Type, req.Page, req.PageSize)
	if err != nil {
		logger.Errorf("查询分享历史失败: userId=%s, error=%v", userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询分享历史失败")
	}

	// 2. 检查是否有数据 - 对应 Java: if (Objects.isNull(hisPage) || CollectionUtils.isEmpty(hisPage.getRecords())) { return ResponseMessage.buildSuccess(hisPage); }
	if hisPage == nil || hisPage.Records == nil || len(hisPage.Records) == 0 {
		logger.Infof("分享历史为空: userId=%s", userId)
		// 如果 hisPage 为 nil，创建一个默认的空分页结果
		if hisPage == nil {
			hisPage = &bo.PageResult[*bo.DeviceShareHisBo]{
				Records: []*bo.DeviceShareHisBo{},
				Total:   0,
				Size:    req.PageSize,
				Current: req.Page,
				Pages:   1,
			}
		} else if hisPage.Records == nil {
			// 如果 Records 为 nil，初始化为空切片
			hisPage.Records = []*bo.DeviceShareHisBo{}
		}
		return webRes.Cb(hisPage)
	}

	// 3. 获取记录列表 - 对应 Java: List<DeviceShareHisVo> his = hisPage.getRecords();
	his := hisPage.Records

	// 4. 处理分享历史信息 - 对应 Java: handleShareHis(his, userId, tenantId);
	logger.Infof("getShareHisTDL,处理分享历史信息")
	s.handleShareHis(his, userId, tenantId)

	// 5. 返回成功响应 - 对应 Java: return ResponseMessage.buildSuccess(hisPage);
	logger.Infof("获取分享历史完成: userId=%s, total=%d, count=%d", userId, hisPage.Total, len(hisPage.Records))
	return webRes.Cb(hisPage)
}

// handleShareHis 处理分享历史信息，补充用户信息、产品信息、家庭信息
// 对应 Java: handleShareHis(List<DeviceShareHisBo> his, String userId, String tenantId)
func (s *deviceInviteShareService) handleShareHis(his []*bo.DeviceShareHisBo, userId, tenantId string) {
	if len(his) == 0 {
		return
	}

	// 使用 WaitGroup 替代 Java 的 CountDownLatch
	var wg sync.WaitGroup
	wg.Add(3)

	// 1. 异步补充用户信息 - 对应 Java: handleUserInfo
	go func() {
		defer wg.Done()
		s.handleUserInfo(his, tenantId, userId)
	}()

	// 2. 异步补充产品信息 - 对应 Java: handleProductInfo
	go func() {
		defer wg.Done()
		s.handleProductInfo(his, tenantId)
	}()

	// 3. 异步补充家庭信息 - 对应 Java: getFamilyShare
	go func() {
		defer wg.Done()
		s.getFamilyShare(his)
	}()

	// 等待所有异步任务完成 - 对应 Java: countDownLatch.await()
	// 注意：Go 的 WaitGroup.Wait() 不会返回错误，但 Java 的 CountDownLatch.await() 可能抛出 InterruptedException
	// 这里我们记录日志但不抛出异常，因为 Go 的 goroutine 机制不同
	wg.Wait()

	// 对应 Java 版本的异常处理日志
	logger.Infof("获取分享历史时，补充分享信息完成")
}

// handleUserInfo 补充用户信息
// 对应 Java: handleUserInfo(List<DeviceShareHisBo> list, String tenantId, String userId, boolean inviter)
func (s *deviceInviteShareService) handleUserInfo(his []*bo.DeviceShareHisBo, tenantId, userId string) {
	logger.Infof("getShareHisTDL,用户信息")

	// 收集所有用户ID
	userIdSet := make(map[string]bool)
	for _, vo := range his {
		userIdSet[vo.InviteId] = true
		userIdSet[vo.BeInviteId] = true
	}

	// 转换为切片
	var userIds []string
	for id := range userIdSet {
		userIds = append(userIds, id)
	}

	if len(userIds) == 0 {
		return
	}

	logger.Infof("getShareHisTDL,用户信息,ids=%v", userIds)

	// 创建带超时的上下文，避免远程调用超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 调用用户服务获取用户信息 - 对应 Java: userRemote.getListByIds(tenantId, userIds)
	response := remote.UserService.GetListByIds(timeoutCtx, tenantId, userIds)
	if response.Fail() {
		logger.Errorf("获取分享信息历史，远程调用获取用户信息失败: tenantId=%s, userIds=%v, error=%s", tenantId, userIds, response.Msg)
		return
	}

	userInfos := response.Result
	if userInfos == nil || len(*userInfos) == 0 {
		return
	}

	logger.Infof("getShareHisTDL,用户信息,用户信息=%+v", *userInfos)

	// 构建用户信息映射
	userMap := make(map[string]*remoteReq.TAuthClientUserInfo)
	for _, user := range *userInfos {
		userMap[user.Id] = user
	}

	// 补充用户信息到分享历史中
	for _, vo := range his {
		logger.Infof("getShareHisTDL,用户信息,循环start")

		// 根据当前用户是邀请者还是被邀请者，设置对应的用户名和头像
		// 对应 Java: 被邀请者，则显示邀请者的用户名与头像
		if userId == vo.BeInviteId {
			if user, exists := userMap[vo.InviteId]; exists {
				// 密文的用户名 - 对应 Java: item.setUsername(split[0]);
				vo.Username = user.UsernameEnc

				// 密文的头像信息 - 对应 Java: 只有家庭分享才设置头像
				if vo.Type == 1 && user.AvatarUrl != "" {
					// 对应 Java: item.setPhotoUrl(AESUtil.Encrypt(split[1], tenantId));
					if encryptedUrl, err := putil.Encrypt(user.AvatarUrl, tenantId); err == nil {
						vo.PhotoUrl = encryptedUrl
					} else {
						logger.Errorf("aes加密失败, user avatarUrl=%s, error=%v", user.AvatarUrl, err)
						vo.PhotoUrl = user.AvatarUrl
					}
				}
			}
		}

		// 对应 Java: 邀请者，则显示被邀者的用户名与头像
		if userId == vo.InviteId {
			logger.Infof("getShareHisTDL,显示被邀者的用户名与头像，被邀请者=%+v", vo)

			if user, exists := userMap[vo.BeInviteId]; exists {
				logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,用户名=%s", user.UsernameEnc)

				// 密文的用户名 - 对应 Java: item.setUsername(split[0]);
				vo.Username = user.UsernameEnc
				logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,username=%s", user.UsernameEnc)
				logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,用户名=%s", vo.Username)

				// 密文的头像信息 - 对应 Java: 只有家庭分享才设置头像
				if vo.Type == 1 && user.AvatarUrl != "" {
					logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,长度2，家庭共享")
					logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,avatarUrl=%s", user.AvatarUrl)

					// 对应 Java: item.setPhotoUrl(AESUtil.Encrypt(split[1], tenantId));
					if encryptedUrl, err := putil.Encrypt(user.AvatarUrl, tenantId); err == nil {
						vo.PhotoUrl = encryptedUrl
					} else {
						logger.Errorf("aes加密失败, user avatarUrl=%s, error=%v", user.AvatarUrl, err)
						vo.PhotoUrl = user.AvatarUrl
					}
				}
			} else {
				logger.Infof("getShareHisTDL,显示被邀者的用户名与头像,头像为空")
			}
		}

		logger.Infof("getShareHisTDL,用户信息,循环end")
	}

	logger.Infof("getShareHisTDL,用户信息,end1111")
	logger.Infof("getShareHisTDL,用户信息,end222")
	logger.Infof("getShareHisTDL,用户信息,end333")
	logger.Infof("getShareHisTDL,用户信息done")
}

// handleProductInfo 补充产品信息
// 对应 Java: handleProductInfo(List<DeviceShareHisBo> list, String tenantId, String userId, boolean inviter)
func (s *deviceInviteShareService) handleProductInfo(his []*bo.DeviceShareHisBo, tenantId string) {
	logger.Infof("getShareHisTDL,产品信息")

	// 收集所有产品型号代码
	codeSet := make(map[string]bool)
	for _, vo := range his {
		if vo.ProductModeCode != nil {
			codeSet[*vo.ProductModeCode] = true
		}
	}

	// 转换为切片
	var codes []string
	for code := range codeSet {
		codes = append(codes, code)
	}

	if len(codes) == 0 {
		return
	}

	// 创建带超时的上下文，避免远程调用超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 调用产品服务获取产品信息 - 对应 Java: productServiceRemote.getListByCode(tenantId, codes)
	response := remote.ProductService.GetListByCode(timeoutCtx, tenantId, codes)
	if response.Fail() {
		logger.Errorf("获取分享信息历史，远程调用获取产品信息失败: tenantId=%s, codes=%v, error=%s", tenantId, codes, response.Msg)
		return
	}

	productInfos := response.Result
	if productInfos == nil || len(*productInfos) == 0 {
		return
	}

	// 构建产品信息映射
	productMap := make(map[string]*remoteRes.ProductModeEntity)
	for _, product := range *productInfos {
		productMap[product.Code] = product
	}

	// 补充产品信息到分享历史中
	for _, vo := range his {
		if product, exists := productMap[*vo.ProductModeCode]; exists {
			if product.Label != "" {
				vo.ModeType = product.Label // 产品型号名称
			}
			if product.PhotoUrl != "" {
				vo.PhotoUrl = product.PhotoUrl // 产品图片URL
			}
			if product.ProductInfoId != "" {
				vo.ProductId = &product.ProductInfoId // 产品ID
			}
			vo.ProductName = product.ProductName // 产品名称
		}
	}

	logger.Infof("getShareHisTDL,产品信息done")
}

// getFamilyShare 补充家庭信息
// 对应 Java: getFamilyShare(List<DeviceShareHisBo> his)
func (s *deviceInviteShareService) getFamilyShare(his []*bo.DeviceShareHisBo) {
	logger.Infof("getShareHisTDL,家庭信息")

	// 收集家庭类型的分享记录的家庭ID
	var familyIds []string
	for _, vo := range his {
		if vo.Type == 1 { // 1-共享家庭
			familyIds = append(familyIds, vo.TargetId)
		}
	}

	if len(familyIds) == 0 {
		return
	}

	// 创建带超时的上下文，避免远程调用超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 调用智能家居服务获取家庭信息 - 对应 Java: smartHomeServiceRemote.getFamilyInfoByIds(familyIds)
	response := remote.SmartHomeService.GetFamilyInfoByIds(timeoutCtx, familyIds)
	if response.Fail() {
		// 对应 Java 版本：只记录错误日志，不返回错误
		logger.Errorf("获取分享信息历史，远程调用获取家庭信息失败: familyIds=%v, error=%s", familyIds, response.Msg)
		return
	}

	familyInfos := response.Result
	if familyInfos == nil || len(*familyInfos) == 0 {
		return
	}

	// 构建家庭信息映射
	familyMap := make(map[string]*remoteReq.FamilyInfoEntity)
	for _, family := range *familyInfos {
		familyMap[family.Id] = family
	}

	// 补充家庭信息到分享历史中
	for _, vo := range his {
		if vo.Type == 1 { // 家庭分享
			if family, exists := familyMap[vo.TargetId]; exists {
				vo.Name = &family.Name // 家庭名称
			}
		}
	}

	logger.Infof("getShareHisTDL,家庭信息done")
}

// InviteUserShareRobot 邀请用户分享设备
// 对应 Java: DeviceInviteShareServiceImpl.inviteUserShareRobot(String beInvited, String inviterId, String inviter, List<String> targetIds)
func (s *deviceInviteShareService) InviteUserShareRobot(ctx context.Context, shareReq *req.ShareReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getContextValue()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userId := ctx.Value(ctxKeys.ID).(string)
	// 被邀请者用户名解密
	beInvited, err := putil.Decrypt(shareReq.BeInvited, tenantId)
	if err != nil {
		logger.Errorf("aes解密失败, beInvited=%s", beInvited)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "被邀请的用户名格式有误")
	}
	logger.Infof("邀请用户分享设备开始: tenantId=%s, userId=%s, beInvited=%s, targetCount=%d",
		tenantId, userId, beInvited, len(shareReq.TargetIds))

	// 1. 通过用户名获取被邀请者信息 - 对应 Java: userRemote.getByUsername(tenantId, shareVo.getBeInvited())
	userResponse := remote.UserService.GetByUsername(ctx, tenantId, beInvited)
	if userResponse.Fail() || userResponse.Result == nil {
		logger.Errorf("获取被邀请者信息失败: beInvited=%s, error=%s", beInvited, userResponse.Msg)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "用户不存在")
	}

	beInvitedUser := userResponse.Result
	beInvitedUserId := beInvitedUser.Id

	// 2. 检查是否为自己分享给自己 - 对应 Java: if (userId.equals(beInvitedUserId))
	if userId == beInvitedUserId {
		logger.Warnf("不能分享给自己: userId=%s, beInvitedUserId=%s", userId, beInvitedUserId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "不能分享给自己")
	}

	// 3. 获取被邀请者扩展信息 - 对应 Java: userRemote.getUserExtByUserId(tenantId, beInvitedUserId)
	userExtResponse := remote.UserService.GetUserExtByUserId(ctx, tenantId, beInvitedUserId)
	if userExtResponse.Fail() || userExtResponse.Result == nil {
		logger.Errorf("获取被邀请者扩展信息失败: beInvitedUserId=%s, error=%s", beInvitedUserId, userExtResponse.Msg)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取用户扩展信息失败")
	}

	beInvitedUserExt := userExtResponse.Result

	// 4. 检查智能家居开关一致性 - 对应 Java: if (!Objects.equals(userExt.getSmartHome(), beInvitedUserExt.getSmartHome()))
	userExtSelfResponse := remote.UserService.GetUserExtByUserId(ctx, tenantId, userId)
	if !userExtSelfResponse.Fail() && userExtSelfResponse.Result != nil {
		userExtSelf := userExtSelfResponse.Result
		if userExtSelf.EnableSmartHome != beInvitedUserExt.EnableSmartHome {
			logger.Warnf("智能家居开关不一致: selfSmartHome=%d, beInvitedSmartHome=%d",
				userExtSelf.EnableSmartHome, beInvitedUserExt.EnableSmartHome)
			return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "分享失败，无效的被分享者")
		}
	}

	// 5. 获取设备信息列表 - 对应 Java: deviceInfoService.getByIds(tenantId, shareVo.getTargetIds())
	devices, err := DeviceInfoService.GetByIds(ctx, tenantId, shareReq.TargetIds)
	if err != nil {
		logger.Errorf("获取设备信息失败: targetIds=%v, error=%v", shareReq.TargetIds, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	if len(devices) == 0 {
		logger.Warnf("没有找到有效的设备: targetIds=%v", shareReq.TargetIds)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "没有找到有效的设备")
	}

	// 6. 检查重复分享并构建插入列表
	var insertList []*domain.DeviceInviteShare
	var pushInfoList []*remoteReq.PushInfo

	for _, device := range devices {
		// 检查是否已存在分享记录 - 对应 Java: count(deviceInviteShareEntity, InviteShareEnum.DEVICE.getStatus(), InviteShareEnum.REJECTED.getStatus(), true) > 0
		exists, err := s.checkShareExist(ctx, userId, beInvitedUserId, device.Id, 0, 2, true)
		if err != nil {
			logger.Errorf("检查分享记录失败: deviceId=%s, error=%v", device.Id, err)
			continue
		}

		if exists {
			deviceNickName := device.Nickname
			if deviceNickName == "" {
				deviceNickName = device.Sn
			}
			logger.Errorf("设备【%s】已共享给【%s】，不可重复共享", deviceNickName, beInvitedUser.Username)
			return webRes.Ce(webErr.NOT_FOUND_DATA, "设备已分享")
		}

		// 创建分享记录 - 对应 Java: new DeviceInviteShareEntity(shareId, inviterId, beInvitedUser.getId(), deviceInfo.getId(), InviteShareEnum.DEVICE.getStatus(), InviteShareEnum.ENABLE.getStatus(), OperatorUtils.getOperateBy(), OperatorUtils.getOperateBy())
		shareEntity := &domain.DeviceInviteShare{
			Id:         putil.Id(),
			TenantId:   tenantId,
			InviteId:   userId,
			BeInviteId: beInvitedUserId,
			TargetId:   device.Id,
			Type:       0, // 设备分享 - 对应 Java: InviteShareEnum.DEVICE.getStatus() = 0
			Status:     0, // 正常状态 - 对应 Java: InviteShareEnum.ENABLE.getStatus() = 0
			Removed:    0, // 未删除
			CreateBy:   userId,
			CreateTime: time.Now(),
			UpdateBy:   userId,
			UpdateTime: time.Now(),
		}

		insertList = append(insertList, shareEntity)

		// 构建推送信息 - 对应 Java: PushInfo pushInfo = new PushInfo()
		pushInfo := &remoteReq.PushInfo{
			TargetId: device.Id,
			ShareId:  shareEntity.Id,
			Sn:       device.Sn,
		}
		pushInfoList = append(pushInfoList, pushInfo)
	}

	// 对应 Java: if (!CollectionUtils.isEmpty(insertList)) { ... } else { return ResponseMessage.buildSuccess(shareIdList); }
	if len(insertList) == 0 {
		logger.Warnf("没有新的设备需要分享: targetIds=%v", shareReq.TargetIds)
		return webRes.Cb(true) // 这里应该返回成功，而不是错误，与Java保持一致
	}

	// 7. 批量保存分享记录 - 对应 Java: this.saveBatch(insertList)
	err = repository.DeviceInviteShareRepository.SaveBatch(ctx, insertList)
	if err != nil {
		logger.Errorf("批量保存分享记录失败: count=%d, error=%v", len(insertList), err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "保存分享记录失败")
	}

	// 8. 异步发送推送消息 - 对应 Java: commonService.pushShare()
	go s.pushShareAsync(ctx, pushInfoList, beInvitedUserId)

	logger.Infof("邀请用户分享设备成功: tenantId=%s, userId=%s, beInvitedUserId=%s, shareCount=%d",
		tenantId, userId, beInvitedUserId, len(insertList))

	// 对应 Java: ResponseMessage.buildSuccess(true)
	return webRes.Cb(true)
}

// pushShareAsync 异步发送分享推送消息
// 对应 Java: commonService.pushShare(jPushShareReq)
func (s *deviceInviteShareService) pushShareAsync(ctx context.Context, pushInfoList []*remoteReq.PushInfo, beInvitedUserId string) {
	if len(pushInfoList) == 0 {
		return
	}

	// 构建推送请求 - 对应 Java: JPushShareReq jPushShareReq = new JPushShareReq(InviteShareEnum.DEVICE.getMsg(), list, InviteShareEnum.DEVICE.getStatus(), InviteShareEnum.ENABLE.getStatus(), beInvitedUser.getId())
	pushReq := &remoteReq.JPushShareReq{
		To:     beInvitedUserId, // 对应 Java: private String to;
		Title:  "设备分享",          // 对应 Java: private String title;
		List:   pushInfoList,
		Type:   0, // 设备分享 - 对应 Java: InviteShareEnum.DEVICE.getStatus() = 0
		Status: 0, // 正常状态 - 对应 Java: InviteShareEnum.ENABLE.getStatus() = 0
	}

	// 发送推送消息 - 对应 Java: infrastructureThirdRemote.pushShare(jPushShareReq)
	response := remote.InfrastructureThirdService.PushShare(ctx, pushReq)
	if response.Fail() {
		logger.Errorf("发送分享推送消息失败: beInvitedUserId=%s, error=%s", beInvitedUserId, response.Msg)
	} else {
		logger.Infof("发送分享推送消息成功: beInvitedUserId=%s, count=%d", beInvitedUserId, len(pushInfoList))
	}
}

// ReplyShared 回应用户的分享
// 对应 Java: DeviceInviteShareServiceImpl.replyShared(int type, String familyId, String familyName, String beInvitedUserId, String inviterId, String targetId, int dealType)
func (s *deviceInviteShareService) ReplyShared(ctx context.Context, replyReq *req.ShareReplyReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getContextValue(ContextKey.TENANT_ID), SystemContextUtils.getId()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	beInvitedUserId := ctx.Value(ctxKeys.ID).(string)

	logger.Infof("回应用户分享开始: tenantId=%s, beInvitedUserId=%s, inviterId=%s, targetId=%s, type=%d, dealType=%d",
		tenantId, beInvitedUserId, replyReq.InviterId, replyReq.TargetId, replyReq.Type, replyReq.DealType)

	var deviceInfo *domain.DeviceInfo

	// 提前判断设备是否存在 - 对应 Java: if (InviteShareEnum.DEVICE.getStatus() == type)
	if replyReq.Type == 0 { // 设备分享
		// 对应 Java: deviceInfo = deviceInfoCache.getCacheById(tenantId, targetId)
		var err error
		deviceInfo, err = cache.DeviceInfoCache.GetCacheById(ctx, tenantId, replyReq.TargetId)
		if err != nil {
			logger.Errorf("获取设备缓存信息失败: targetId=%s, error=%v", replyReq.TargetId, err)
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
		}

		// 对应 Java: if (Objects.isNull(deviceInfo))
		if deviceInfo == nil {
			logger.Errorf("回应分享时，设备不存在: type=%d, beInvitedUserId=%s, inviterId=%s, targetId=%s, dealType=%d",
				replyReq.Type, beInvitedUserId, replyReq.InviterId, replyReq.TargetId, replyReq.DealType)
			return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
		}
	}

	// 查询分享记录 - 对应 Java: DeviceInviteShareEntity inviteShare = this.baseMapper.selectOne(queryWrapper)
	inviteShare, err := repository.DeviceInviteShareRepository.FindByCondition(ctx,
		replyReq.InviterId, beInvitedUserId, replyReq.TargetId, replyReq.Type)
	if err != nil {
		logger.Errorf("查询分享记录失败: inviterId=%s, beInvitedUserId=%s, targetId=%s, type=%d, error=%v",
			replyReq.InviterId, beInvitedUserId, replyReq.TargetId, replyReq.Type, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询分享记录失败")
	}

	// 对应 Java: if (Objects.isNull(inviteShare))
	if inviteShare == nil {
		logger.Warnf("分享记录不存在: inviterId=%s, beInvitedUserId=%s, targetId=%s, type=%d",
			replyReq.InviterId, beInvitedUserId, replyReq.TargetId, replyReq.Type)
		// 对应 Java: return false - 返回 false 表示分享记录不存在
		// 使用指针类型以匹配 Java 的 Boolean 包装类型
		result := false
		return webRes.Cb(&result)
	}

	// 处理回应类型 - 对应 Java: switch (dealType)
	var status int
	switch replyReq.DealType {
	case 1: // 同意 - 对应 Java: case 1: status = InviteShareEnum.AGREED.getStatus()
		status = 1 // 同意状态(InviteShareEnum.AGREED.getStatus() = 1)

		// 共享设备 - 对应 Java: if (InviteShareEnum.DEVICE.getStatus() == type)
		if replyReq.Type == 0 {
			// 检测设备是否还属于共享者 - 对应 Java: deviceBindUserMapper.getDeviceInfoAndBindByUserId(inviterId)
			deviceList, err := repository.DeviceBindUserRepository.GetDeviceInfoAndBindByUserId(ctx, replyReq.InviterId, tenantId)
			if err != nil {
				logger.Errorf("获取邀请者绑定设备列表失败: inviterId=%s, error=%v", replyReq.InviterId, err)
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取邀请者绑定设备列表失败")
			}

			// 检查设备是否还在邀请者的设备列表中 - 对应 Java: deviceInfoAndBindByUserId.stream().noneMatch(b->targetId.equals(b.getDeviceId()))
			deviceExists := false
			for _, device := range deviceList {
				if device.DeviceId == replyReq.TargetId {
					deviceExists = true
					break
				}
			}

			if !deviceExists {
				// 更新分享记录 - 对应 Java: deviceInviteShareMapper.updateById(update)
				err = repository.DeviceInviteShareRepository.UpdateStatusById(ctx, inviteShare.Id, status)
				if err != nil {
					logger.Errorf("更新分享记录状态失败: shareId=%s, status=%d, error=%v", inviteShare.Id, status, err)
				}
				// 对应 Java: throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在")
				return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
			}

			// 绑定房间 - 对应 Java: doBindRoom(familyId, beInvitedUserId, targetId, inviterId, deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId())
			success := s.doBindRoom(ctx, replyReq.FamilyId, beInvitedUserId, replyReq.TargetId,
				replyReq.InviterId, deviceInfo.Nickname, deviceInfo.Sn, deviceInfo.ProductId)
			if !success {
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "设备分享异常")
			}
		} else if replyReq.Type == 1 { // 家庭分享 - 对应 Java: else if (InviteShareEnum.FAMILY.getStatus() == type)
			// 绑定家庭 - 对应 Java: doBindFamily(dealType, targetId, inviterId, beInvitedUserId)
			success := s.doBindFamily(ctx, replyReq.DealType, replyReq.TargetId, replyReq.InviterId, beInvitedUserId, tenantId)
			if !success {
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "家庭分享异常")
			}
		} else {
			// 对应 Java: throw new IllegalArgumentException("type:" + type)
			return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "type:"+fmt.Sprintf("%d", replyReq.Type))
		}

	case 2: // 拒绝 - 对应 Java: case 2: status = InviteShareEnum.REJECTED.getStatus()
		status = 2 // 拒绝状态(InviteShareEnum.REJECTED.getStatus() = 2)
		// 对应 Java: doBindFamily(dealType, targetId, inviterId, beInvitedUserId)
		success := s.doBindFamily(ctx, replyReq.DealType, replyReq.TargetId, replyReq.InviterId, beInvitedUserId, tenantId)
		if !success {
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "家庭分享异常")
		}

	default:
		// 对应 Java: throw new IllegalArgumentException("dealType:" + dealType)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "dealType:"+fmt.Sprintf("%d", replyReq.DealType))
	}

	// 更新分享记录状态 - 对应 Java: deviceInviteShareMapper.updateById(inviteShare)
	err = repository.DeviceInviteShareRepository.UpdateStatusById(ctx, inviteShare.Id, status)
	if err != nil {
		logger.Errorf("更新分享记录状态失败: shareId=%s, status=%d, error=%v", inviteShare.Id, status, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "更新分享记录状态失败")
	}

	// 发送推送消息 - 对应 Java: commonService.pushShare(req)
	go s.pushReplyShareAsync(ctx, replyReq, inviteShare.Id, deviceInfo, status)

	logger.Infof("回应用户分享成功: beInvitedUserId=%s, inviterId=%s, targetId=%s, type=%d, dealType=%d",
		beInvitedUserId, replyReq.InviterId, replyReq.TargetId, replyReq.Type, replyReq.DealType)

	// 对应 Java: return true - 返回 true 表示回应分享成功
	// 使用指针类型以匹配 Java 的 Boolean 包装类型
	result := true
	return webRes.Cb(&result)
}

// doBindRoom 绑定房间
// 对应 Java: doBindRoom(String familyId, String beInvitedUserId, String deviceId, String owner, String nickname, String sn, String productInfoId)
func (s *deviceInviteShareService) doBindRoom(ctx context.Context, familyId, beInvitedUserId, deviceId, owner, nickname, sn, productInfoId string) bool {
	// 获取被邀请者扩展信息检查智能家居开关 - 对应 Java: userRemote.getUserExtByUserId(SystemContextUtils.getTenantId(), beInvitedUserId)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userExtResponse := remote.UserService.GetUserExtByUserId(ctx, tenantId, beInvitedUserId)
	if userExtResponse.Fail() || userExtResponse.Result == nil {
		logger.Errorf("获取被邀请者扩展信息失败: beInvitedUserId=%s, error=%s", beInvitedUserId, userExtResponse.Msg)
		return false
	}

	beInvitedUserExt := userExtResponse.Result

	// 智能家居开关检查 - 对应 Java: if (Constants.openSmartHome.equals(String.valueOf(beInvitedUserExt.getEnableSmartHome())))
	if beInvitedUserExt.EnableSmartHome == 1 { // 开启智能家居 - 对应 Java: Constants.openSmartHome = "1"
		// 远程调用smart-home-service服务 - 对应 Java: smartHomeServiceRemote.bindShareDevice(new RoomBindShareDeviceReq(...))
		bindReq := &remoteReq.RoomBindShareDeviceReq{
			UserId:    beInvitedUserId,
			FamilyId:  familyId,
			DeviceId:  deviceId,
			Nickname:  nickname,
			Sn:        sn,
			Owner:     owner,
			ProductId: productInfoId,
		}

		response := remote.SmartHomeService.BindShareDevice(ctx, bindReq)
		if response.Fail() {
			logger.Errorf("接受分享时，绑定设备异常，tenantId=%s, familyId=%s, beInviterId=%s, deviceId=%s, owner=%t, nickname=%s, msg=%s",
				tenantId, familyId, beInvitedUserId, deviceId, owner, nickname, response.Msg)
			return false
		}
		return *response.Result
	} else {
		// 非智能家居模式 - 对应 Java: deviceBindUserService.shareBind(deviceId, nickname, sn, productInfoId, owner)
		return DeviceBindUserService.ShareBind(ctx, deviceId, nickname, sn, productInfoId, owner)
	}
}

// doBindFamily 绑定家庭
// 对应 Java: doBindFamily(int status, String targetId, String inviteId, String beInviteId)
func (s *deviceInviteShareService) doBindFamily(ctx context.Context, status int, targetId, inviteId, beInviteId, tenantId string) bool {
	// 构建家庭分享回应请求 - 对应 Java: ShareFamilyReplyReq shareFamilyReply = new ShareFamilyReplyReq(...)
	confirmReq := &remoteReq.ShareFamilyReplyReq{
		TargetId:   targetId,
		InviteId:   inviteId,
		BeInviteId: beInviteId,
		Status:     status,
		TenantId:   tenantId,
	}

	// 远程调用确认家庭分享 - 对应 Java: smartHomeServiceRemote.confirm(shareFamilyReply)
	response := remote.SmartHomeService.Confirm(ctx, confirmReq)
	if response.Fail() {
		logger.Errorf("接受分享时，家庭成员绑定异常，tenantId=%s, familyId=%s, beInviteId=%s, inviteId=%s, status=%d, msg=%s",
			tenantId, targetId, beInviteId, inviteId, status, response.Msg)
		return false
	}

	// 如果智能家居服务返回false，目前只有管理员撤销分享这种情况 - 对应 Java: if (status == InviteShareEnum.AGREED.getStatus() && !confirm.getResult())
	if status == 1 && !*response.Result { // AGREED状态且返回false
		logger.Infof("接受分享时，管理员已撤销分享，tenantId=%s, familyId=%s, beInviteId=%s, inviteId=%s, status=%d, msg=%s",
			tenantId, targetId, beInviteId, inviteId, status, response.Msg)
		// 对应 Java: throw new AppRuntimeException(ILLEGAL_STATE.getCode(), "管理员已撤销分享,无法加入家庭")
		// 这里返回false，让上层处理异常
		return false
	}

	return *response.Result
}

// pushReplyShareAsync 异步发送回应分享推送消息
// 对应 Java: commonService.pushShare(req)
func (s *deviceInviteShareService) pushReplyShareAsync(ctx context.Context, replyReq *req.ShareReplyReq, shareId string, deviceInfo *domain.DeviceInfo, status int) {
	// 整合极光推送的额外信息 - 对应 Java: PushInfo info = new PushInfo(targetId, inviteShare.getId())
	pushInfo := &remoteReq.PushInfo{
		TargetId: replyReq.TargetId,
		ShareId:  shareId,
	}

	// 根据分享类型设置额外信息
	if replyReq.Type == 0 && deviceInfo != nil { // 设备分享
		pushInfo.Sn = deviceInfo.Sn
	} else if replyReq.Type == 1 { // 家庭分享
		pushInfo.FamilyName = replyReq.FamilyName
	}

	// 构建推送请求 - 对应 Java: JPushShareReq req = new JPushShareReq(...)
	title := "设备分享"
	if replyReq.Type == 1 {
		title = "家庭分享"
	}

	pushReq := &remoteReq.JPushShareReq{
		To:     replyReq.InviterId, // 被邀请者回应分享，则发送极光消息给邀请者
		Title:  title,
		List:   []*remoteReq.PushInfo{pushInfo},
		Type:   replyReq.Type,
		Status: status,
	}

	// 发送推送消息 - 对应 Java: infrastructureThirdRemote.pushShare(req)
	response := remote.InfrastructureThirdService.PushShare(ctx, pushReq)
	if response.Fail() {
		logger.Errorf("发送回应分享推送消息失败: inviterId=%s, error=%s", replyReq.InviterId, response.Msg)
	} else {
		logger.Infof("发送回应分享推送消息成功: inviterId=%s", replyReq.InviterId)
	}
}

// DelByInvitee 根据被邀请者删除分享记录
// 对应 Java: InviteShareServiceImpl.delByInvitee(String beInviteeId, String targetId, int type)
func (s *deviceInviteShareService) DelByInvitee(ctx context.Context, beInviteeId, targetId string, shareType int) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 删除分享记录 - 对应 Java: LambdaQueryWrapper<DeviceInviteShareEntity> queryWrapper = new LambdaQueryWrapper<>()
		err := repository.DeviceInviteShareRepository.DeleteByCondition(ctx, beInviteeId, targetId, shareType)
		if err != nil {
			logger.Errorf("删除分享记录失败: beInviteeId=%s, targetId=%s, type=%d, error=%v",
				beInviteeId, targetId, shareType, err)
		} else {
			logger.Infof("删除分享记录成功: beInviteeId=%s, targetId=%s, type=%d",
				beInviteeId, targetId, shareType)
		}
	}()
}

// CancelShare 取消分享
// 对应 Java: DeviceInviteShareServiceImpl.cancelShare(String shareId, String userId)
func (s *deviceInviteShareService) CancelShare(ctx context.Context, shareId, userId string) bool {
	logger.Infof("取消分享开始: shareId=%s, userId=%s", shareId, userId)

	// 查询分享记录 - 对应 Java: QueryWrapper<DeviceInviteShareEntity> wrapper = new QueryWrapper<>(); wrapper.lambda().eq(DeviceInviteShareEntity::getId, shareId);
	inviteShare, err := repository.DeviceInviteShareRepository.FindById(ctx, shareId)
	if err != nil {
		logger.Errorf("查询分享记录失败: shareId=%s, error=%v", shareId, err)
		return false
	}

	// 对应 Java: if (inviteShare == null) throw new AppRuntimeException(NOT_FOUND_DATA);
	if inviteShare == nil {
		logger.Warnf("分享记录不存在: shareId=%s", shareId)
		return false
	}

	logger.Infof("获取的inviteShare: %+v", inviteShare)

	// 判断是否为拥有者 - 对应 Java: boolean owner = false; if (Objects.equals(userId, inviteShare.getInviteId())) owner = true;
	owner := userId == inviteShare.InviteId

	shareType := inviteShare.Type
	status := inviteShare.Status

	// 删除分享记录 - 对应 Java: int del = deviceInviteShareMapper.deleteById(shareId);
	err = repository.DeviceInviteShareRepository.DeleteById(ctx, shareId)
	if err != nil {
		logger.Errorf("删除分享记录失败: shareId=%s, error=%v", shareId, err)
		return false
	}

	logger.Infof("删除分享信息结果: shareId=%s", shareId)

	// 设备共享的，已经删除成功，并且不是拒绝状态的设备共享，才解除设备与房间的绑定
	// 对应 Java: if ((type == InviteShareEnum.DEVICE.getStatus()) && del > 0 && InviteShareEnum.REJECTED.getStatus() != inviteShare.getStatus())
	if shareType == 0 && status != 2 { // 设备分享(InviteShareEnum.DEVICE.getStatus() = 0)且状态不是拒绝(InviteShareEnum.REJECTED.getStatus() = 2)
		res := s.unShareRoom(ctx, inviteShare.TargetId, inviteShare.InviteId, inviteShare.BeInviteId, owner)
		if owner {
			// 对应 Java: commonService.shareInvalidate(inviteShare.getTargetId(), inviteShare.getBeInviteId(), res);
			s.shareInvalidate(ctx, inviteShare.TargetId, inviteShare.BeInviteId, res)
		}
		return res
	}

	// 只有（正常或者同意）的并且是共享家庭的，才进行远程调用
	// 对应 Java: if ((type == InviteShareEnum.FAMILY.getStatus()) && del > 0 && (status == InviteShareEnum.ENABLE.getStatus() || status == InviteShareEnum.AGREED.getStatus()))
	if shareType == 1 && (status == 0 || status == 1) { // 家庭分享(InviteShareEnum.FAMILY.getStatus() = 1)且状态为正常(InviteShareEnum.ENABLE.getStatus() = 0)或已同意(InviteShareEnum.AGREED.getStatus() = 1)
		// 对应 Java: RevokeDto revokeDto = new RevokeDto(inviteShare.getBeInviteId(), inviteShare.getTargetId(), inviteShare.getInviteId());
		revokeDto := remoteReq.NewRevokeDto(inviteShare.BeInviteId, inviteShare.TargetId, inviteShare.InviteId)

		// 对应 Java: ResponseMessage<Boolean> revoke = smartHomeServiceRemote.revoke(revokeDto);
		response := remote.SmartHomeService.Revoke(ctx, revokeDto)
		if response.Fail() {
			logger.Errorf("共享家庭撤销失败，beinviteId=%s, familyId=%s, inviteId=%s, exceptionMsg=%s",
				inviteShare.BeInviteId, inviteShare.TargetId, inviteShare.InviteId, response.Msg)
			return false
		}

		// owner推送消息给所有正在使用的被分享者
		// 对应 Java: if (owner) commonService.shareInvalidate(inviteShare.getTargetId(), inviteShare.getBeInviteId(), revoke.getResult());
		if owner {
			s.shareInvalidate(ctx, inviteShare.TargetId, inviteShare.BeInviteId, *response.Result)
		}
		return true
	}

	logger.Errorf("删除分享异常，更新异常, shareId=%s, userId=%s", shareId, userId)
	return false
}

// unShareRoom 解除设备与房间的绑定
// 对应 Java: DeviceInviteShareServiceImpl.unShareRoom(String deviceId, String inviter, String beInviter, boolean owner)
func (s *deviceInviteShareService) unShareRoom(ctx context.Context, deviceId, inviter, beInviter string, owner bool) bool {
	// 获取被分享者用户信息 - 对应 Java: ResponseMessage<TAuthClientUserExt> beInviterUserExtRes = userRemote.getUserExtByUserId(SystemContextUtils.getTenantId(), beInviter);
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	beInviterUserExtRes := remote.UserService.GetUserExtByUserId(ctx, tenantId, beInviter)
	if beInviterUserExtRes.Fail() {
		logger.Errorf("远程调用通过用户ID获取用户信息EXT失败: beInvited=%s, error=%s", beInviter, beInviterUserExtRes.Msg)
		return false
	}

	// 智能家居开关检查 - 对应 Java: if (Constants.openSmartHome.equals(String.valueOf(beInviterUserExtRes.getResult().getEnableSmartHome())))
	if beInviterUserExtRes.Result.EnableSmartHome == 1 { // 开启智能家居 - 对应 Java: Constants.openSmartHome = "1"
		// 远程调用，删除对应的绑定关系 - 对应 Java: smartHomeServiceRemote.userUntieDevice(new UserUniteShareDeviceReq(deviceId, inviter, beInviter, owner));
		userUntieReq := remoteReq.NewUserUniteShareDeviceReq(deviceId, inviter, beInviter, owner)
		response := remote.SmartHomeService.UserUntieDevice(ctx, userUntieReq)
		if response.Fail() {
			logger.Errorf("删除共享消息时，删除对应的绑定关系失败，deviceId=%s, inviter=%s, owner=%t, exceptionMsg=%s",
				deviceId, inviter, owner, response.Msg)
			return false
		}
		return true
	}

	// 非智能家居场景 - 对应 Java: return deviceBindUserService.untieShareBind(deviceId, beInviter);
	return DeviceBindUserService.UntieShareBind(ctx, deviceId, beInviter)
}

// shareInvalidate 分享失效
// 对应 Java: CommonService.shareInvalidate(String targetId,String uid,boolean pushMsg)
func (s *deviceInviteShareService) shareInvalidate(ctx context.Context, targetId, uid string, pushMsg bool) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 设置分享消息为无效状态 - 对应 Java: infrastructureThirdRemote.invalidateMsgByTargetIdAndUid(targetId,uid);
		response := remote.InfrastructureThirdService.InvalidateMsgByTargetIdAndUid(ctx, targetId, uid)
		if response.Fail() {
			logger.Errorf("设置分享消息为无效状态失败: targetId=%s, uid=%s, error=%s", targetId, uid, response.Msg)
		} else {
			logger.Infof("设置分享消息为无效状态成功: targetId=%s, uid=%s", targetId, uid)
		}

		// 推送用户取消解绑 - 对应 Java: if(pushMsg) infrastructureThirdRemote.pushCancelShareMsg(targetId,uid);
		if pushMsg {
			pushResponse := remote.InfrastructureThirdService.PushCancelShareMsg(ctx, targetId, uid)
			if pushResponse.Fail() {
				logger.Errorf("推送用户取消解绑消息失败: targetId=%s, uid=%s, error=%s", targetId, uid, pushResponse.Msg)
			} else {
				logger.Infof("推送用户取消解绑消息成功: targetId=%s, uid=%s", targetId, uid)
			}
		}
	}()
}

// DelByDeviceId 根据设备ID删除分享记录
// 对应 Java: DeviceInviteShareServiceImpl.delByDeviceId(String deviceId)
func (s *deviceInviteShareService) DelByDeviceId(ctx context.Context, deviceId string) bool {
	logger.Infof("根据设备ID删除分享记录开始: deviceId=%s", deviceId)

	// 删除分享记录 - 对应 Java: LambdaQueryWrapper<DeviceInviteShareEntity> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(DeviceInviteShareEntity::getTargetId, deviceId).eq(DeviceInviteShareEntity::getType, InviteShareEnum.DEVICE.getStatus());
	err := repository.DeviceInviteShareRepository.DeleteByTargetIdAndType(ctx, deviceId, 0) // 设备分享类型(InviteShareEnum.DEVICE.getStatus() = 0)
	if err != nil {
		logger.Errorf("根据设备ID删除分享记录失败: deviceId=%s, error=%v", deviceId, err)
		return false
	}

	logger.Infof("根据设备ID删除分享记录成功: deviceId=%s", deviceId)
	return true
}

// DelShare 删除分享记录
// 对应 Java: DeviceInviteShareServiceImpl.delShare(DelShareReq req) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) DelShare(ctx context.Context, req *req.DelShareReq) webRes.IBaseRes {
	// 对应 Java: if (Objects.isNull(req)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if req == nil {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: String userId = SystemContextUtils.getId();
	userId := ctx.Value(ctxKeys.ID).(string)

	// 对应 Java: List<String> deviceIds = req.getDeviceId();
	deviceIds := req.DeviceId

	// 对应 Java: String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "tenantId"); }
	if tenantId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
	}

	// 对应 Java: boolean inviter = req.isInviter();
	inviter := req.Inviter

	// 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "deviceId"); }
	if len(deviceIds) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	logger.Infof("删除分享记录开始: userId=%s, tenantId=%s, inviter=%v, deviceIds=%v", userId, tenantId, inviter, deviceIds)

	// 对应 Java: UpdateWrapper<DeviceInviteShareEntity> wrapper = new UpdateWrapper<>();
	// 对应 Java: LambdaUpdateWrapper<DeviceInviteShareEntity> lambda = wrapper.lambda();
	var deleteCount int64
	var err error

	if inviter {
		// 对应 Java: lambda.eq(DeviceInviteShareEntity::getInviteId, userId); lambda.set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.DISABLE.getStatus());
		deleteCount, err = repository.DeviceInviteShareRepository.DeleteByInviterAndDeviceIds(ctx, userId, deviceIds, 0) // 禁用状态(InviteShareEnum.DISABLE.getStatus() = 0)
	} else {
		// 对应 Java: lambda.eq(DeviceInviteShareEntity::getBeInviteId, userId); lambda.set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.REMOVED.getStatus());
		deleteCount, err = repository.DeviceInviteShareRepository.DeleteByBeInviteAndDeviceIds(ctx, userId, deviceIds, 2) // 移除状态(InviteShareEnum.REMOVED.getStatus() = 2)
	}

	if err != nil {
		logger.Errorf("删除分享记录失败: userId=%s, inviter=%v, deviceIds=%v, error=%v", userId, inviter, deviceIds, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除分享记录失败")
	}

	// 对应 Java: int delete = deviceInviteShareMapper.delete(wrapper); return ResponseMessage.buildSuccess(delete > 0);
	result := deleteCount > 0
	logger.Infof("删除分享记录完成: userId=%s, inviter=%v, deviceIds=%v, deleteCount=%d, result=%v",
		userId, inviter, deviceIds, deleteCount, result)

	return webRes.Cb(result)
}

// DoShareFamily 分享家庭
// 对应 Java: DeviceInviteShareServiceImpl.doShareFamily(ShareFamilyVo vo) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) DoShareFamily(ctx context.Context, vo *req.ShareFamilyReq) webRes.IBaseRes {
	logger.Infof("分享家庭开始: targetId=%s, familyName=%s, beInviteIds=%v", vo.TargetId, vo.FamilyName, vo.BeInviteIds)

	// 对应 Java: if (Objects.isNull(vo)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if vo == nil {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: String inviterId = SystemContextUtils.getId();
	inviterId := ctx.Value(ctxKeys.ID).(string)
	targetId := vo.TargetId
	beInvitedIds := vo.BeInviteIds
	familyName := vo.FamilyName

	// 对应 Java: if (StringUtils.isBlank(targetId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "targetId"); }
	if targetId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "targetId")
	}

	// 对应 Java: if (CollectionUtils.isEmpty(beInvitedIds)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "未选中成员"); }
	if len(beInvitedIds) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "未选中成员")
	}

	// 对应 Java: List<DeviceInviteShareEntity> insertList = new ArrayList<>(beInvitedIds.size());
	var insertList []*domain.DeviceInviteShare
	// 对应 Java: List<Map<String, String>> list = new ArrayList<>(beInvitedIds.size());
	var pushList []map[string]string
	// 对应 Java: List<String> shareIdList = new ArrayList<>(beInvitedIds.size());
	var shareIdList []string

	// 对应 Java: for (String beInviteId : beInvitedIds) { ... }
	for _, beInviteId := range beInvitedIds {
		// 对应 Java: String id = SnowflakeUtil.snowflakeId();
		id := putil.Id()

		// 对应 Java: DeviceInviteShareEntity deviceInviteShareEntity = new DeviceInviteShareEntity(id, inviterId, beInviteId, targetId, InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(), OperatorUtils.getOperateBy(), OperatorUtils.getOperateBy());
		deviceInviteShareEntity := &domain.DeviceInviteShare{
			Id:         id,
			InviteId:   inviterId,
			BeInviteId: beInviteId,
			TargetId:   targetId,
			Type:       1, // 对应 Java: InviteShareEnum.FAMILY.getStatus() = 1
			Status:     0, // 对应 Java: InviteShareEnum.ENABLE.getStatus() = 0
			CreateBy:   inviterId,
			UpdateBy:   inviterId,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		}

		// 对应 Java: if (count(deviceInviteShareEntity, InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(), false) > 0) { continue; }
		exists, err := s.checkShareExist(ctx, inviterId, beInviteId, targetId, 1, 0, false)
		if err != nil {
			logger.Errorf("检查分享记录失败: beInviteId=%s, targetId=%s, error=%v", beInviteId, targetId, err)
			continue
		}
		if exists {
			continue
		}

		// 对应 Java: Map<String, String> map = new HashMap<>(NumberEnum.TWO.getNumber()); map.put("to", beInviteId); map.put("shareId", id); list.add(map);
		pushMap := map[string]string{
			"to":      beInviteId,
			"shareId": id,
		}
		pushList = append(pushList, pushMap)

		// 对应 Java: shareIdList.add(deviceInviteShareEntity.getId()); insertList.add(deviceInviteShareEntity);
		shareIdList = append(shareIdList, id)
		insertList = append(insertList, deviceInviteShareEntity)
	}

	// 对应 Java: if (!CollectionUtils.isEmpty(insertList)) { this.saveBatch(insertList); ... }
	if len(insertList) > 0 {
		err := repository.DeviceInviteShareRepository.SaveBatch(ctx, insertList)
		if err != nil {
			logger.Errorf("批量保存分享记录失败: insertList=%+v, error=%v", insertList, err)
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "保存分享记录失败")
		}

		// 对应 Java: JPushFamilyShareReq jPushFamilyShareReq = new JPushFamilyShareReq(InviteShareEnum.FAMILY.getMsg(), InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(), targetId, familyName, list); commonService.pushFamilyShare(jPushFamilyShareReq);
		pushReq := &remoteReq.JPushFamilyShareReq{
			Title:      "家庭分享", // 对应 Java: InviteShareEnum.FAMILY.getMsg()
			Type:       1,      // 对应 Java: InviteShareEnum.FAMILY.getStatus() = 1
			Status:     0,      // 对应 Java: InviteShareEnum.ENABLE.getStatus() = 0
			TargetId:   targetId,
			FamilyName: familyName,
			List:       pushList,
		}
		remote.InfrastructureThirdService.PushFamilyShare(ctx, pushReq)

		logger.Infof("分享家庭成功: targetId=%s, familyName=%s, shareCount=%d", targetId, familyName, len(shareIdList))
		return webRes.Cb(shareIdList)
	}

	// 对应 Java: return ResponseMessage.buildSuccess(shareIdList);
	logger.Infof("分享家庭完成: targetId=%s, familyName=%s, shareCount=%d", targetId, familyName, len(shareIdList))
	return webRes.Cb(shareIdList)
}

// DelByUserId 根据用户ID删除分享记录
// 对应 Java: DeviceInviteShareServiceImpl.delByUserId(String tenantId, String userId) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) DelByUserId(ctx context.Context, tenantId, userId string) bool {
	logger.Infof("根据用户ID删除分享记录: tenantId=%s, userId=%s", tenantId, userId)

	// 对应 Java: int delete = deviceInviteShareMapper.deleteByUserId(tenantId, userId); return delete > 0;
	deleteCount, err := repository.DeviceInviteShareRepository.DeleteByUserId(ctx, tenantId, userId)
	if err != nil {
		logger.Errorf("根据用户ID删除分享记录失败: tenantId=%s, userId=%s, error=%v", tenantId, userId, err)
		return false
	}

	result := deleteCount > 0
	logger.Infof("根据用户ID删除分享记录完成: tenantId=%s, userId=%s, deleteCount=%d, result=%v", tenantId, userId, deleteCount, result)
	return result
}

// GetShareRobot 获取用户的分享设备信息
// 对应 Java: DeviceInviteShareServiceImpl.getShareRobot(GetUserSharedReq req) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) GetShareRobot(ctx context.Context, req *req.GetUserSharedReq) webRes.IBaseRes {
	logger.Infof("获取用户的分享设备信息: inviter=%v, targetId=%s, page=%d, pageSize=%d", req.Inviter, req.TargetId, req.Page, req.PageSize)

	// 对应 Java: String userId = SystemContextUtils.getId();
	userId := ctx.Value(ctxKeys.ID).(string)

	var pageResult *bo.PageResult[*vo.DeviceShareVo]
	var err error

	// 对应 Java: if (req.isInviter()) { page = deviceInviteShareMapper.getListByInviter(page, userId, req.getTargetId()); } else { page = deviceInviteShareMapper.getListByBeInviter(page, userId, req.getTargetId()); }
	if req.Inviter {
		pageResult, err = repository.DeviceInviteShareRepository.GetListByInviter(ctx, userId, req.TargetId, req.Page, req.PageSize)
	} else {
		pageResult, err = repository.DeviceInviteShareRepository.GetListByBeInviter(ctx, userId, req.TargetId, req.Page, req.PageSize)
	}

	if err != nil {
		logger.Errorf("获取分享设备信息失败: inviter=%v, targetId=%s, error=%v", req.Inviter, req.TargetId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取分享设备信息失败")
	}

	// 对应 Java: List<DeviceShareVo> records = page.getRecords();
	records := pageResult.Records

	// 对应 Java: if (CollectionUtils.isNotEmpty(records)) { ... }
	if len(records) > 0 {
		// 对应 Java: 多线程处理用户信息、产品信息、家庭信息
		s.processShareRecords(ctx, records)
	}

	logger.Infof("获取用户的分享设备信息成功: inviter=%v, targetId=%s, total=%d", req.Inviter, req.TargetId, pageResult.Total)
	return webRes.Cb(pageResult)
}

// processShareRecords 处理分享记录信息
// 对应 Java: 多线程处理用户信息、产品信息、家庭信息 (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) processShareRecords(ctx context.Context, records []*vo.DeviceShareVo) {
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: 收集需要查询的用户ID和家庭ID
	var userIds []string
	var familyIds []string
	userIdMap := make(map[string]bool)
	familyIdMap := make(map[string]bool)

	for _, record := range records {
		// 对应 Java: 收集用户ID
		if record.UserId != "" && !userIdMap[record.UserId] {
			userIds = append(userIds, record.UserId)
			userIdMap[record.UserId] = true
		}

		// 对应 Java: 收集家庭ID (type=2表示家庭分享)
		if record.Type == 2 && record.TargetId != "" && !familyIdMap[record.TargetId] {
			familyIds = append(familyIds, record.TargetId)
			familyIdMap[record.TargetId] = true
		}
	}

	// 对应 Java: 并发查询用户信息和家庭信息
	var userInfoMap map[string]*remoteReq.TAuthClientUserInfo
	var familyInfoMap map[string]*remoteReq.FamilyInfoEntity

	// 查询用户信息
	if len(userIds) > 0 {
		userInfoMap = s.getUserInfoMap(ctx, tenantId, userIds)
	}

	// 查询家庭信息
	if len(familyIds) > 0 {
		familyInfoMap = s.getFamilyInfoMap(ctx, familyIds)
	}

	// 对应 Java: 填充记录信息
	for _, record := range records {
		// 对应 Java: 填充用户名
		if userInfo, exists := userInfoMap[record.UserId]; exists && userInfo != nil {
			record.Username = userInfo.Username
		}

		// 对应 Java: 填充家庭名称 (type=2表示家庭分享)
		if record.Type == 2 {
			if familyInfo, exists := familyInfoMap[record.TargetId]; exists && familyInfo != nil {
				record.Name = familyInfo.Name
			}
		}
	}
}

// getUserInfoMap 获取用户信息映射
// 对应 Java: 查询用户信息的逻辑 (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) getUserInfoMap(ctx context.Context, tenantId string, userIds []string) map[string]*remoteReq.TAuthClientUserInfo {
	userInfoMap := make(map[string]*remoteReq.TAuthClientUserInfo)

	// 创建带超时的上下文，避免远程调用超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 对应 Java: ResponseMessage<List<TAuthClientUserInfo>> userListResult = userRemote.getListByIds(tenantId, userIds);
	userListResult := remote.UserService.GetListByIds(timeoutCtx, tenantId, userIds)
	if !userListResult.Fail() {
		for _, userInfo := range *userListResult.Result {
			if userInfo != nil {
				userInfoMap[userInfo.Id] = userInfo
			}
		}
	}

	return userInfoMap
}

// getFamilyInfoMap 获取家庭信息映射
// 对应 Java: 查询家庭信息的逻辑 (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) getFamilyInfoMap(ctx context.Context, familyIds []string) map[string]*remoteReq.FamilyInfoEntity {
	familyInfoMap := make(map[string]*remoteReq.FamilyInfoEntity)

	// 创建带超时的上下文，避免远程调用超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 对应 Java: ResponseMessage<List<FamilyInfoEntity>> familyListResult = smartHomeServiceRemote.getFamilyInfoByIds(familyIds);
	familyListResult := remote.SmartHomeService.GetFamilyInfoByIds(timeoutCtx, familyIds)
	if !familyListResult.Fail() {
		for _, familyInfo := range *familyListResult.Result {
			if familyInfo != nil {
				familyInfoMap[familyInfo.Id] = familyInfo
			}
		}
	}

	return familyInfoMap
}

// ChangeShareRecordStatus 通过极光推送的消息拒绝或同意分享
// 对应 Java: DeviceInviteShareServiceImpl.changeShareRecordStatus(ShareRecordChangeStatusReq req) (遵循规则47: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) ChangeShareRecordStatus(ctx context.Context, req *req.ShareRecordChangeStatusReq) webRes.IBaseRes {
	logger.Infof("通过极光推送的消息拒绝或同意分享: shareId=%s, recordId=%s, status=%d", req.ShareId, req.RecordId, req.Status)

	// 对应 Java: String shareId = req.getShareId(); String recordId = req.getRecordId(); Integer status = req.getStatus(); String familyId = req.getFamilyId(); String familyName = req.getFamilyName();
	shareId := req.ShareId
	recordId := req.RecordId
	status := req.Status
	familyId := req.FamilyId
	familyName := req.FamilyName

	// 对应 Java: if (StringUtils.isBlank(shareId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "shareId"); }
	if shareId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "shareId")
	}

	// 对应 Java: if (StringUtils.isBlank(recordId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "recordId"); }
	if recordId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "recordId")
	}

	// 对应 Java: ResponseMessage<NoticeShareRecordEntity> responseMessage = commonService.checkShareRecord(recordId);
	responseMessage := CommonService.CheckShareRecord(ctx, recordId)

	// 对应 Java: if (!responseMessage.isSuccess()) { LogUtils.error("通过分享记录id获取分享记录信息失败，recordId={}", recordId); return ResponseMessage.buildFail(INNER_SERVER_ERROR); }
	if responseMessage.Fail() {
		logger.Errorf("通过分享记录id获取分享记录信息失败，recordId=%s", recordId)
		return webErr.INNER_SERVER_ERROR
	}

	// 对应 Java: NoticeShareRecordEntity one = responseMessage.getResult();
	one := responseMessage.(*webRes.SRes[*res.NoticeShareRecord]).Result

	// 对应 Java: if (Objects.isNull(one)) { return ResponseMessage.buildSuccess(false); }
	if one == nil {
		return webRes.Cb(false)
	}

	// 对应 Java: if (Objects.isNull(status) || (!Objects.equals(status, InviteShareEnum.AGREED.getStatus()) && !Objects.equals(InviteShareEnum.REJECTED.getStatus(), status))) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "status 非法"); }
	if status != 1 && status != 2 { // 对应 Java: InviteShareEnum.AGREED.getStatus() = 1, InviteShareEnum.REJECTED.getStatus() = 2
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "status 非法")
	}

	// 对应 Java: if (Objects.equals(InviteShareEnum.FAMILY.getStatus(), one.getType()) && StringUtils.isBlank(familyName)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "familyName"); }
	if one.Type != nil && *one.Type == 1 && familyName == "" { // 家庭分享类型(InviteShareEnum.FAMILY.getStatus() = 1)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "familyName")
	}

	// 对应 Java: boolean result = changeStatus(familyName, familyId, shareId, status);
	result := s.changeStatus(ctx, familyName, familyId, shareId, status)

	// 对应 Java: return ResponseMessage.buildSuccess(result);
	return webRes.Cb(result)
}

// changeStatus 修改分享状态
// 对应 Java: DeviceInviteShareServiceImpl.changeStatus(String familyName, String familyId, String shareId, Integer status) (遵循规则47: 严格按照Java编码内容转换)
func (s *deviceInviteShareService) changeStatus(ctx context.Context, familyName, familyId, shareId string, status int) bool {
	logger.Infof("修改分享状态: shareId=%s, status=%d, familyId=%s, familyName=%s", shareId, status, familyId, familyName)

	// 对应 Java: String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID); String beinvitedId = SystemContextUtils.getId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	beinvitedId := ctx.Value(ctxKeys.ID).(string)

	// 对应 Java: DeviceInviteShareEntity inviteShareEntity = this.getById(shareId);
	inviteShareEntity, err := repository.DeviceInviteShareRepository.GetById(ctx, shareId)
	if err != nil {
		logger.Errorf("查询分享记录失败: shareId=%s, error=%v", shareId, err)
		return false
	}

	// 对应 Java: if (Objects.isNull(inviteShareEntity)) { LogUtils.error("分享记录不存在，shareId={}", shareId); return false; }
	if inviteShareEntity == nil {
		logger.Errorf("分享记录不存在，shareId=%s", shareId)
		return false
	}

	// 对应 Java: String inviterId = inviteShareEntity.getInviteId(); String targetId = inviteShareEntity.getTargetId(); int type = inviteShareEntity.getType();
	inviterId := inviteShareEntity.InviteId
	targetId := inviteShareEntity.TargetId
	inviteType := inviteShareEntity.Type

	// 对应 Java: switch (status) { case 1: ... case 2: ... }
	switch status {
	case 1: // 对应 Java: case 1: // 同意
		// 对应 Java: inviteShareEntity.setStatus(InviteShareEnum.AGREED.getStatus());
		inviteShareEntity.Status = 1 // 对应 Java: InviteShareEnum.AGREED.getStatus() = 1

		// 对应 Java: if (InviteShareEnum.DEVICE.getStatus() == type) { ... } else if (InviteShareEnum.FAMILY.getStatus() == type) { ... }
		if inviteType == 0 { // 对应 Java: InviteShareEnum.DEVICE.getStatus() = 0
			// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, targetId);
			deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, targetId)
			if err != nil || deviceInfo == nil {
				logger.Errorf("设备不存在: targetId=%s, tenantId=%s, error=%v", targetId, tenantId, err)
				// 对应 Java: throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在");
				return false
			}

			// 对应 Java: doBindRoom(familyId, beInvitedUserId, targetId, inviterId, deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId());
			return s.doBindRoom(ctx, familyId, beinvitedId, targetId, inviterId, deviceInfo.Nickname, deviceInfo.Sn, deviceInfo.ProductId)
		} else if inviteType == 1 { // 对应 Java: InviteShareEnum.FAMILY.getStatus() = 1
			// 对应 Java: doBindFamily(dealType, targetId, inviterId, beInvitedUserId);
			return s.doBindFamily(ctx, status, targetId, inviterId, beinvitedId, tenantId)
		} else {
			logger.Errorf("type非法: type=%d", inviteType)
			return false
		}
	case 2: // 对应 Java: case 2: // 拒绝
		// 对应 Java: inviteShare.setStatus(InviteShareEnum.REJECTED.getStatus());
		inviteShareEntity.Status = 2 // 拒绝状态(InviteShareEnum.REJECTED.getStatus() = 2)

		// 对应 Java: doBindFamily(dealType, targetId, inviterId, beInvitedUserId);
		return s.doBindFamily(ctx, status, targetId, inviterId, beinvitedId, tenantId)
	default:
		logger.Errorf("status非法: status=%d", status)
		return false
	}
}

// checkShareExist 检查分享记录是否存在
// 对应 Java: DeviceInviteShareServiceImpl.count(DeviceInviteShareEntity shareEntity, int type, int status, boolean ne)
func (s *deviceInviteShareService) checkShareExist(ctx context.Context, inviteId, beInviteId, targetId string, type_ int, status int, ne bool) (bool, error) {
	count, err := repository.DeviceInviteShareRepository.Count(ctx, inviteId, beInviteId, targetId, type_, status, ne)
	if err != nil {
		logger.Errorf("检查分享记录失败: inviteId=%s, beInviteId=%s, targetId=%s, type=%d, status=%d, ne=%v, error=%v",
			inviteId, beInviteId, targetId, type_, status, ne, err)
		return false, err
	}
	return count > 0, nil
}
