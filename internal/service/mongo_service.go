package service

import (
	"context"
	"github.com/spf13/viper"

	"go.3irobotix.net/aiot/common-log/logger"
	"go.mongodb.org/mongo-driver/v2/bson"
	"piceacorp.com/device-service/internal/dao/mongo"
	mongoDb "piceacorp.com/device-service/pkg/data/mongo"
)

// IMongoService Mongo服务接口
// 对应 Java: IMongoService
type IMongoService interface {
	// 单条保存 - 对应 Java: void insert(String userId, String sn)
	Insert(ctx context.Context, userId, sn string)
	// 单条删除 - 对应 Java: void remove(String userId, String sn)
	Remove(ctx context.Context, userId, sn string)
	// 用户列表批量保存 - 对应 Java: void batchInsertByUserIds(List<String> userIds, String sn)
	BatchInsertByUserIds(ctx context.Context, userIds []string, sn string)
	// 用户列表批量删除 - 对应 Java: void batchRemoveByUserIds(List<String> userIds, String sn)
	BatchRemoveByUserIds(ctx context.Context, userIds []string, sn string)
	// 设备SN列表批量保存 - 对应 Java: void batchInsertBySns(String userId, List<String> sns)
	BatchInsertBySns(ctx context.Context, userId string, sns []string)
	// 设备SN列表批量删除 - 对应 Java: void batchRemoveBySns(String userId, List<String> sns)
	BatchRemoveBySns(ctx context.Context, userId string, sns []string)
}

var MongoService mongoService

type mongoService struct {
}

func (s *mongoService) enabled() bool {
	return viper.GetBool("user.device.relation")
}

// Insert 单条保存
// 对应 Java: MongoServiceImpl.insert(String userId, String sn)
func (s *mongoService) Insert(ctx context.Context, userId, sn string) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 对应 Java: if (enabled)

		if !s.enabled() {
			logger.Debugf(ctx, "MongoDB未启用，跳过保存绑定关系: userId=%s, sn=%s", userId, sn)
			return
		}

		logger.Infof(ctx, "Mongo保存绑定关系开始: userId=%s, sn=%s", userId, sn)

		// 对应 Java: DeviceBindCollection deviceBindCollection = new DeviceBindCollection();
		deviceBindCollection := mongo.NewDeviceBindCollection(userId, sn)

		// 对应 Java: mongoTemplate.save(deviceBindCollection);
		collection := mongoDb.DB.Collection(deviceBindCollection.CollectionName())
		_, err := collection.InsertOne(ctx, deviceBindCollection)
		if err != nil {
			logger.Errorfm(ctx, "Mongo保存绑定关系失败: userId=%s, sn=%s, error=%v", userId, sn, err)
			return
		}

		logger.Infof(ctx, "Mongo保存绑定关系成功: userId=%s, sn=%s", userId, sn)
	}()
}

// Remove 单条删除
// 对应 Java: MongoServiceImpl.remove(String userId, String sn)
func (s *mongoService) Remove(ctx context.Context, userId, sn string) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 对应 Java: if (enabled)
		if !s.enabled() {
			logger.Debugf(ctx, "MongoDB未启用，跳过删除绑定关系: userId=%s, sn=%s", userId, sn)
			return
		}

		logger.Infof(ctx, "Mongo删除绑定关系开始: userId=%s, sn=%s", userId, sn)

		// 对应 Java: Query query = Query.query(Criteria.where("id").is(getId(userId, sn)));
		filter := bson.M{"_id": mongo.GetID(userId, sn)}

		// 对应 Java: mongoTemplate.remove(query, DeviceBindCollection.class);
		collection := mongoDb.DB.Collection("device_bind_rel")
		result, err := collection.DeleteOne(ctx, filter)
		if err != nil {
			logger.Errorfm(ctx, "Mongo删除绑定关系失败: userId=%s, sn=%s, error=%v", userId, sn, err)
			return
		}

		logger.Infof(ctx, "Mongo删除绑定关系成功: userId=%s, sn=%s, deletedCount=%d", userId, sn, result.DeletedCount)
	}()
}

// BatchInsertByUserIds 用户列表批量保存
// 对应 Java: MongoServiceImpl.batchInsertByUserIds(List<String> userIds, String sn)
func (s *mongoService) BatchInsertByUserIds(ctx context.Context, userIds []string, sn string) {
	// 对应 Java: if (enabled && CollectionUtil.isNotEmpty(userIds))
	if !s.enabled() || len(userIds) == 0 {
		logger.Debugf(ctx, "MongoDB未启用或用户列表为空，跳过批量保存: userIds=%v, sn=%s", userIds, sn)
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		logger.Infof(ctx, "Mongo批量保存绑定关系开始: userIds=%v, sn=%s", userIds, sn)

		collection := mongoDb.DB.Collection("device_bind_rel")

		// 对应 Java: userIds.forEach(userId -> { ... mongoTemplate.save(deviceBindCollection); });
		for _, userId := range userIds {
			deviceBindCollection := mongo.NewDeviceBindCollection(userId, sn)
			_, err := collection.InsertOne(ctx, deviceBindCollection)
			if err != nil {
				logger.Errorfm(ctx, "Mongo批量保存绑定关系失败: userId=%s, sn=%s, error=%v", userId, sn, err)
				continue
			}
			logger.Debugf(ctx, "Mongo保存绑定关系成功: userId=%s, sn=%s", userId, sn)
		}

		logger.Infof(ctx, "Mongo批量保存绑定关系完成: userIds=%v, sn=%s", userIds, sn)
	}()
}

// BatchRemoveByUserIds 用户列表批量删除
// 对应 Java: MongoServiceImpl.batchRemoveByUserIds(List<String> userIds, String sn)
func (s *mongoService) BatchRemoveByUserIds(ctx context.Context, userIds []string, sn string) {
	// 对应 Java: if (enabled && CollectionUtil.isNotEmpty(userIds))
	if !s.enabled() || len(userIds) == 0 {
		logger.Debugf(ctx, "MongoDB未启用或用户列表为空，跳过批量删除: userIds=%v, sn=%s", userIds, sn)
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		logger.Infof(ctx, "Mongo批量删除绑定关系开始: userIds=%v, sn=%s", userIds, sn)

		// 对应 Java: List<String> ids = new ArrayList<>(userIds.size()); userIds.forEach(userId -> { ids.add(getId(userId, sn)); });
		var ids []string
		for _, userId := range userIds {
			ids = append(ids, mongo.GetID(userId, sn))
		}

		// 对应 Java: Query query = Query.query(Criteria.where("id").in(ids));
		filter := bson.M{"_id": bson.M{"$in": ids}}

		// 对应 Java: mongoTemplate.remove(query, DeviceBindCollection.class);
		collection := mongoDb.DB.Collection("device_bind_rel")
		result, err := collection.DeleteMany(ctx, filter)
		if err != nil {
			logger.Errorfm(ctx, "Mongo批量删除绑定关系失败: userIds=%v, sn=%s, error=%v", userIds, sn, err)
			return
		}

		logger.Infof(ctx, "Mongo批量删除绑定关系成功: userIds=%v, sn=%s, deletedCount=%d", userIds, sn, result.DeletedCount)
	}()
}

// BatchInsertBySns 设备SN列表批量保存
// 对应 Java: MongoServiceImpl.batchInsertBySns(String userId, List<String> sns)
func (s *mongoService) BatchInsertBySns(ctx context.Context, userId string, sns []string) {
	// 对应 Java: if (enabled && CollectionUtil.isNotEmpty(sns))
	if !s.enabled() || len(sns) == 0 {
		logger.Debugf(ctx, "MongoDB未启用或SN列表为空，跳过批量保存: userId=%s, sns=%v", userId, sns)
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		logger.Infof(ctx, "Mongo批量保存绑定关系开始: userId=%s, sns=%v", userId, sns)

		collection := mongoDb.DB.Collection("device_bind_rel")

		// 对应 Java: sns.forEach(sn -> { ... mongoTemplate.save(deviceBindCollection); });
		for _, sn := range sns {
			deviceBindCollection := mongo.NewDeviceBindCollection(userId, sn)
			_, err := collection.InsertOne(ctx, deviceBindCollection)
			if err != nil {
				logger.Errorfm(ctx, "Mongo批量保存绑定关系失败: userId=%s, sn=%s, error=%v", userId, sn, err)
				continue
			}
			logger.Debugf(ctx, "Mongo保存绑定关系成功: userId=%s, sn=%s", userId, sn)
		}

		logger.Infof(ctx, "Mongo批量保存绑定关系完成: userId=%s, sns=%v", userId, sns)
	}()
}

// BatchRemoveBySns 设备SN列表批量删除
// 对应 Java: MongoServiceImpl.batchRemoveBySns(String userId, List<String> sns)
func (s *mongoService) BatchRemoveBySns(ctx context.Context, userId string, sns []string) {
	// 对应 Java: if (enabled && CollectionUtil.isNotEmpty(sns))
	if !s.enabled() || len(sns) == 0 {
		logger.Debugf(ctx, "MongoDB未启用或SN列表为空，跳过批量删除: userId=%s, sns=%v", userId, sns)
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		logger.Infof(ctx, "Mongo批量删除绑定关系开始: userId=%s, sns=%v", userId, sns)

		// 对应 Java: List<String> ids = new ArrayList<>(sns.size()); sns.forEach(sn -> { ids.add(getId(userId, sn)); });
		var ids []string
		for _, sn := range sns {
			ids = append(ids, mongo.GetID(userId, sn))
		}

		// 对应 Java: Query query = Query.query(Criteria.where("id").in(ids));
		filter := bson.M{"_id": bson.M{"$in": ids}}

		// 对应 Java: mongoTemplate.remove(query, DeviceBindCollection.class);
		collection := mongoDb.DB.Collection("device_bind_rel")
		result, err := collection.DeleteMany(ctx, filter)
		if err != nil {
			logger.Errorfm(ctx, "Mongo批量删除绑定关系失败: userId=%s, sns=%v, error=%v", userId, sns, err)
			return
		}

		logger.Infof(ctx, "Mongo批量删除绑定关系成功: userId=%s, sns=%v, deletedCount=%d", userId, sns, result.DeletedCount)
	}()
}
