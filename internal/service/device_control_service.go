package service

import (
	"context"
	"encoding/json"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/enum"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/service/remote"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceControlService 设备控制服务接口
// 对应 Java: DeviceControlService (遵循规则43: 必须全部转换)
type IDeviceControlService interface {
	// 控制设备 - 对应 Java: controlDevice(DeviceControlReq req, String tenantId)
	ControlDevice(ctx context.Context, req *req.DeviceControlReq, tenantId string) webRes.IBaseRes
}

var DeviceControlService deviceControlService

type deviceControlService struct{}

// 常量定义 - 对应 Java: private static final int
const (
	// 成功 - 对应 Java: private static final int SUCCESS = 0;
	SUCCESS = 0
	// 设备离线 - 对应 Java: private static final int ROBOT_OFFLINE = 1;
	ROBOT_OFFLINE = 1
	// 获取设备影子失败 - 对应 Java: private static final int GET_SHADOW_FAIL = 2;
	GET_SHADOW_FAIL = 2
)

// ControlDevice 控制设备
// 对应 Java: DeviceControlService.controlDevice(DeviceControlReq req, String tenantId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceControlService) ControlDevice(ctx context.Context, req *req.DeviceControlReq, tenantId string) webRes.IBaseRes {
	logger.Infof(ctx, "设备远程控制开始: deviceId=%s, tenantId=%s, content=%s", req.DeviceId, tenantId, req.Content)

	// 对应 Java: final DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, req.getDeviceId());
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, req.DeviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备缓存失败: deviceId=%s, error=%v", req.DeviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	if deviceInfo == nil {
		logger.Errorfm(ctx, "设备不存在: deviceId=%s", req.DeviceId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: //通过设备sn获取设备影子
	// 对应 Java: final ResponseMessage<DeviceShadowEntity> response = deviceShadowRemote.findBySN(deviceInfo.getSn());
	// 对应 Java: final DeviceShadowEntity deviceShadow = response.getResult();
	deviceShadow, err := remote.DeviceShadowService.FindBySN(ctx, deviceInfo.Sn)
	if err != nil {
		logger.Errorfm(ctx, "调用设备影子服务失败: sn=%s, error=%v", deviceInfo.Sn, err)
		return webRes.Cb(GET_SHADOW_FAIL)
	}

	// 对应 Java: if (Objects.isNull(deviceShadow)) { LogUtils.error("设备远程控制，远程调用获取设备影子失败，req={}", JsonUtils.toJSON(req)); return ResponseMessage.buildSuccess(GET_SHADOW_FAIL); }
	if deviceShadow == nil {
		logger.Errorfm(ctx, "设备远程控制，远程调用获取设备影子失败，req=%+v", req)
		return webRes.Cb(GET_SHADOW_FAIL)
	}

	// 对应 Java: //设备离线
	// 对应 Java: if (!deviceShadow.getOnlineStatus()) { return ResponseMessage.buildSuccess(ROBOT_OFFLINE); }
	if !deviceShadow.OnlineStatus {
		return webRes.Cb(ROBOT_OFFLINE)
	}

	// 对应 Java: //发送控制命令
	// 对应 Java: kafkaService.senServiceInvoke(req.getDeviceId(), tenantId, MqttContentUtil.joinContent(req.getContent()), DevicePushTag.CONTROL, Constant.SERVICE_INVOKE_KAFKA_TOPIC);

	content := s.joinContent(ctx, req.Content)
	// 确保这些常量值与Java版本一致
	topic := "serviceInvoke" // 对应 Java常量: Constant.SERVICE_INVOKE_KAFKA_TOPIC

	err = KafkaService.SenServiceInvoke(ctx, req.DeviceId, tenantId, content, enum.CONTROL.Tag, topic)

	// 对应 Java: return ResponseMessage.buildSuccess(SUCCESS);
	return webRes.Cb(SUCCESS)
}

// joinContent 拼接内容
// 对应 Java: MqttContentUtil.joinContent(req.getContent()) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceControlService) joinContent(ctx context.Context, content string) string {
	// 对应 Java: private static String contentKey = "command";
	contentKey := "command"

	// 对应 Java: JSONObject json = new JSONObject();
	jsonObj := make(map[string]interface{})

	// 对应 Java: json.put(contentKey, content);
	jsonObj[contentKey] = content

	// 对应 Java: return json.toJSONString();
	jsonBytes, err := json.Marshal(jsonObj)
	if err != nil {
		logger.Errorfm(ctx, "序列化JSON失败: content=%s, error=%v", content, err)
		// 在出错的情况下，返回原始内容，避免完全失败
		return content
	}

	return string(jsonBytes)
}
