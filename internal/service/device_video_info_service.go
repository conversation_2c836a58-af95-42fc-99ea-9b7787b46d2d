package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/dao/repository"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceVideoInfoService 设备视频信息服务接口
// 对应 Java: IDeviceVideoInfoService
type IDeviceVideoInfoService interface {
	// 根据SN获取视频流三元组信息
	GetKey(ctx context.Context, sn string) webRes.IBaseRes
}

var DeviceVideoInfoService deviceVideoInfoService

type deviceVideoInfoService struct{}

// GetKey 根据SN获取视频流三元组信息
// 对应 Java: DeviceVideoInfoServiceImpl.getKey(String sn)
func (s *deviceVideoInfoService) GetKey(ctx context.Context, sn string) webRes.IBaseRes {
	logger.Infof(ctx, "获取视频流三元组信息开始: sn=%s", sn)

	// 1. 根据SN查询设备视频信息 - 对应 Java: this.baseMapper.selectOne(queryWrapper)
	deviceVideoInfo, err := repository.DeviceVideoInfoRepository.GetBySn(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "查询设备视频信息失败: sn=%s, error=%v", sn, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备视频信息失败")
	}

	// 2. 检查设备视频信息是否存在 - 对应 Java: if (Objects.isNull(infoEntity)) { return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA); }
	if deviceVideoInfo == nil {
		logger.Warnf(ctx, "设备视频信息不存在: sn=%s", sn)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备视频信息不存在")
	}

	// 3. 构建响应对象 - 对应 Java: VideoInfoVo result = new VideoInfoVo(); result.setDeviceName(...); result.setProductKey(...); result.setDeviceSecret(...);
	result := &res.VideoInfoRes{
		DeviceName:   deviceVideoInfo.DeviceName,
		ProductKey:   deviceVideoInfo.ProductKey,
		DeviceSecret: deviceVideoInfo.DeviceSecret,
	}

	logger.Infof(ctx, "获取视频流三元组信息成功: sn=%s, deviceName=%s", sn, result.DeviceName)

	// 4. 返回成功响应 - 对应 Java: return ResponseMessage.buildSuccess(result);
	return webRes.Cb(result)
}
