package service

import (
	"context"
	"strings"
	"time"

	"github.com/spf13/viper"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/internal/util"
	redis "piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceBindService 设备绑定服务接口
type IDeviceBindService interface {
	// 绑定确认
	BindConfirm(ctx context.Context, req *req.BindConfirmReq) webRes.IBaseRes
	// 设备发起绑定APP请求
	BindApp(ctx context.Context, req *req.BindAppReq) webRes.IBaseRes
	// 二维码绑定
	OrCodeBind(ctx context.Context, orCodeReq *req.OrCodeBindReq) webRes.IBaseRes
	// 获取bindKey
	GetBindKey(ctx context.Context, deviceId string) webRes.IBaseRes
}

var DeviceBindService deviceBindService

type deviceBindService struct{}

// BindConfirm app确认设备发起的绑定app请求
// 对应 Java: DeviceBindServiceImpl.bindAppConfirm(String userId, String key, String familyId, String roomId)
func (s *deviceBindService) BindConfirm(ctx context.Context, req *req.BindConfirmReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: String tenantId = SystemContextUtils.getContextValue(TENANT_ID); String userId = SystemContextUtils.getId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userId := ctx.Value(ctxKeys.ID).(string)

	logger.Infof("gg: tenantId=%s, userId=%s, bindKey=%s, familyId=%s, roomId=%s",
		tenantId, userId, req.BindKey, req.FamilyId, req.RoomId)

	// 上下文参数验证 - 对应 Java 控制器中的验证
	if tenantId == "" {
		logger.Errorf("租户ID为空: tenantId=%s", tenantId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
	}

	if userId == "" {
		logger.Errorf("用户ID为空: userId=%s", userId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId")
	}

	// 业务参数验证已由 validator 框架在控制器层处理
	// 对应 Java: if (StringUtils.isBlank(key)) { throw new ParameterValidateException("bindKey验证不通过"); }

	// 1. 从缓存获取 bindKey - 对应 Java: bindKeyCache.getBindKey(userId, tenantId, DeviceKey.USER)
	cacheBindKeyObj, err := cache.BindKeyCache.GetBindKey(ctx, userId, tenantId, cache.DeviceKeyUser)
	if err != nil {
		logger.Errorf("获取缓存 bindKey 失败: userId=%s, tenantId=%s, error=%v", userId, tenantId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取绑定信息失败")
	}

	// 兼容开放平台 UID 的 fallback 逻辑 - 对应 Java: SystemContextUtils.getContextValue(ContextKey.OPEN_USER_ID)
	if cacheBindKeyObj == nil {
		openUserId := ctx.Value(ctxKeys.OPEN_USER_ID)
		if openUserId != nil && openUserId.(string) != "" {
			cacheBindKeyObj, err = cache.BindKeyCache.GetBindKey(ctx, openUserId.(string), tenantId, cache.DeviceKeyUser)
			if err != nil {
				logger.Errorf("获取开放平台用户缓存 bindKey 失败: openUserId=%s, tenantId=%s, error=%v", openUserId, tenantId, err)
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取绑定信息失败")
			}
		}
	}

	// 对应 Java: throw new NoDataFoundException("缓存中无bindKey")
	if cacheBindKeyObj == nil {
		logger.Warnf("缓存中未找到 bindKey: userId=%s, tenantId=%s", userId, tenantId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "缓存中无bindKey")
	}

	// 2. 验证 bindKey 一致性 - 对应 Java: throw new ParameterValidateException("bindKey验证不通过")
	if cacheBindKeyObj.BindKey != req.BindKey {
		logger.Warnf("bindKey 不匹配: cache=%s, request=%s", cacheBindKeyObj.BindKey, req.BindKey)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "bindKey验证不通过,缓存中的设备的bindKey和app的bindKey不一致")
	}

	// 3. 从缓存的 BindKey 对象获取设备ID - 对应 Java: bindKey.getDeviceId()
	deviceId := cacheBindKeyObj.DeviceId
	if deviceId == "" {
		logger.Errorf("缓存的 BindKey 中设备ID为空: bindKey=%+v", cacheBindKeyObj)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "绑定信息中设备ID为空")
	}

	// 4. 检查设备是否存在 - 对应 Java: deviceInfoCache.getCacheById(tenantId, deviceId)
	device, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorf("查询设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	// 5. 执行绑定逻辑 - 严格按照Java实现
	isNew := false
	familyId := req.FamilyId
	roomId := req.RoomId

	// 开启智能家居 - 对应 Java: if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME)))
	enableSmartHome := ctx.Value(ctxKeys.ENABLE_SMART_HOME)
	if enableSmartHome != nil && enableSmartHome.(string) == "1" {
		// 对应 Java: if (StringUtils.isBlank(familyId)) throw new ParameterValidateException("familyId");
		if familyId == "" {
			return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "familyId")
		}

		// 远程调用保存用户房间设备之间的绑定关系 - 对应 Java: smartHomeServiceRemote.bindDevice(new RoomBindDeviceReq(...))
		bindReq := remoteReq.NewRoomBindDeviceReq(familyId, roomId, deviceId, device.Nickname, device.Sn, device.ProductId)
		responseMessage := remote.SmartHomeService.BindDevice(ctx, bindReq)

		// 对应 Java: if (!responseMessage.isSuccess()) throw new AppRuntimeException("绑定房间异常");
		if responseMessage.Fail() {
			logger.Errorf("机器发起绑定APP请求后，APP最终确认，发生异常.userId=%s,deviceId=%s,tenantId=%s,key=%s,familyId=%s,roomId=%s,responseMessage=%s",
				userId, deviceId, tenantId, req.BindKey, familyId, roomId, responseMessage.Msg)
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "绑定房间异常")
		}

		// 对应 Java: if (responseMessage.getResult()) inviteShareService.delByDeviceId(deviceId);
		if responseMessage.Result != nil && *responseMessage.Result {
			// 重新配网的用户如果不是该设备的拥有者，则删除对应设备的分享记录
			DeviceInviteShareService.DelByDeviceId(ctx, deviceId)
		}

		isNew = responseMessage.Result != nil && *responseMessage.Result
	} else {
		// 非智能家居绑定流程 - 对应 Java: isNew = deviceBindUserService.bind(deviceId, deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId());
		isNew = DeviceBindUserService.Bind(ctx, deviceId, device.Nickname, device.Sn, device.ProductId)
	}

	// 向语控第三方服务器发送新增绑定信息 - 对应 Java: if (checkIfVoiceControl())
	if s.checkIfVoiceControl(ctx) {
		syncReq := remoteReq.NewSyncBindReq(tenantId, deviceId, userId)
		syncResult := remote.CloudIntentService.SyncBindInfo(ctx, syncReq)
		if syncResult.Fail() || (syncResult.Result != nil && !*syncResult.Result) {
			logger.Errorf("向第三方平台同步新增绑定关系失败，tenantId:%s,userId:%s,deviceId:%s", tenantId, deviceId, userId)
		}
	}

	// 清除设备已签署的协议记录 - 对应 Java: contentServiceRemote.delByDeviceId(deviceId);
	delResponse := remote.ContentService.DelByDeviceId(ctx, deviceId)
	if delResponse.Fail() {
		logger.Warnf("清除设备已签署的协议记录失败: deviceId=%s, error=%s", deviceId, delResponse.Msg)
	}

	// 构建响应 - 对应 Java: BindResp respEntity = new BindResp();
	respEntity := &res.BindConfirmRes{
		IsNew:    isNew,
		Sn:       device.Sn,
		DeviceId: deviceId,
	}

	// 缓存管理 - 对应 Java: 确认绑定成功后的缓存管理逻辑
	s.manageBindKeyCache(ctx, userId, tenantId, deviceId, req.BindKey)

	logger.Infof("设备绑定确认完成: deviceId=%s, userId=%s, isNew=%v", deviceId, userId, isNew)
	return webRes.Cb(respEntity)
}

// OrCodeBind 二维码绑定
// 对应 Java: DeviceBindServiceImpl.orCodeBind(OrCodeBindVo vo)
func (s *deviceBindService) OrCodeBind(ctx context.Context, orCodeReq *req.OrCodeBindReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getId(), SystemContextUtils.getTenantId()
	userId := ctx.Value(ctxKeys.ID).(string)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	bindKey := orCodeReq.BindKey
	familyId := orCodeReq.FamilyId
	roomId := orCodeReq.RoomId
	deviceId := orCodeReq.DeviceId

	logger.Infof("二维码绑定开始: userId=%s, tenantId=%s, deviceId=%s, bindKey=%s, familyId=%s, roomId=%s",
		userId, tenantId, deviceId, bindKey, familyId, roomId)

	// 参数验证 - 对应 Java: if (StringUtils.isBlank(bindKey)) return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "bindKey");
	if bindKey == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "bindKey")
	}

	// 智能家居开关检查 - 对应 Java: if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME)) && StringUtils.isBlank(familyId))
	enableSmartHome := ctx.Value(ctxKeys.ENABLE_SMART_HOME)
	if enableSmartHome != nil && enableSmartHome.(string) == "1" && familyId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "familyId")
	}

	if deviceId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	// 获取设备信息 - 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorf("获取设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	// 对应 Java: if (Objects.isNull(deviceInfo))
	if deviceInfo == nil {
		logger.Errorf("二维码绑定时，设备不存在，deviceId=%s, bindKey=%s, tenantId=%s, userId=%s", deviceId, bindKey, tenantId, userId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 获取缓存中的bindKey - 对应 Java: BindKey cacheBindKey = bindKeyCache.getBindKey(deviceId, tenantId, DeviceKey.DEVICE);
	cacheBindKey, err := cache.BindKeyCache.GetBindKey(ctx, deviceId, tenantId, cache.DeviceKeyDevice)
	if err != nil {
		logger.Errorf("获取缓存bindKey失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取缓存bindKey失败")
	}

	logger.Infof("cacheBindKey缓存信息: %+v", cacheBindKey)

	// 对应 Java: if (Objects.isNull(cacheBindKey))
	if cacheBindKey == nil {
		logger.Errorf("二维码绑定时，bindKey不存在，deviceId=%s, bindKey=%s, tenantId=%s, userId=%s", deviceId, bindKey, tenantId, userId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "bindKey不存在")
	}

	// 验证bindKey - 对应 Java: if (!Objects.equals(bindKey, cacheBindKey.getBindKey()))
	if bindKey != cacheBindKey.BindKey {
		logger.Errorf("二维码绑定时，bindKey校验不通过，deviceId=%s, bindKey=%s, tenantId=%s, userId=%s", deviceId, bindKey, tenantId, userId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "bindKey验证不通过，服务器颁发的的bindKey与绑定时的bindKey不一致")
	}

	// 执行绑定逻辑
	var isNew bool

	// 开启智能家居 - 对应 Java: if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME)))
	if enableSmartHome != nil && enableSmartHome.(string) == "1" {
		// 智能家居绑定流程 - 对应 Java: smartHomeServiceRemote.bindDevice(new RoomBindDeviceReq(...))
		bindReq := remoteReq.NewRoomBindDeviceReq(familyId, roomId, deviceId, deviceInfo.Nickname, deviceInfo.Sn, deviceInfo.ProductId)
		response := remote.SmartHomeService.BindDevice(ctx, bindReq)

		if response.Fail() {
			logger.Errorf("扫码绑定时，绑定设备异常，userId=%s,deviceId=%s,tenantId=%s,bindKey=%s,familyId=%s, roomId=%s,responseMessage=%s",
				userId, deviceId, tenantId, bindKey, familyId, roomId, response.Msg)
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "绑定房间异常")
		}

		if response.Result != nil && *response.Result {
			// 重新配网的用户如果不是该设备的拥有者，则删除对应设备的分享记录 - 对应 Java: inviteShareService.delByDeviceId(deviceId);
			DeviceInviteShareService.DelByDeviceId(ctx, deviceId)
		}

		isNew = response.Result != nil && *response.Result
	} else {
		// 非智能家居绑定流程 - 对应 Java: isNew = deviceBindUserService.bind(deviceId, deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId());
		isNew = DeviceBindUserService.Bind(ctx, deviceId, deviceInfo.Nickname, deviceInfo.Sn, deviceInfo.ProductId)
	}

	// 向语控第三方服务器发送新增绑定信息 - 对应 Java: if (checkIfVoiceControl())
	if s.checkIfVoiceControl(ctx) {
		syncReq := remoteReq.NewSyncBindReq(tenantId, deviceId, userId)
		syncResult := remote.CloudIntentService.SyncBindInfo(ctx, syncReq)
		if syncResult.Fail() || (syncResult.Result != nil && !*syncResult.Result) {
			logger.Errorf("向第三方平台同步新增绑定关系失败，tenantId:%s,userId:%s,deviceId:%s", tenantId, deviceId, userId)
		}
	}

	// 清除设备已签署的协议记录 - 对应 Java: contentServiceRemote.delByDeviceId(deviceId);
	delResponse := remote.ContentService.DelByDeviceId(ctx, deviceId)
	if delResponse.Fail() {
		logger.Warnf("清除设备已签署的协议记录失败: deviceId=%s, error=%s", deviceId, delResponse.Msg)
	}

	// 构建响应 - 对应 Java: BindResp respEntity = new BindResp();
	respEntity := &res.BindConfirmRes{
		IsNew:    isNew,
		Sn:       deviceInfo.Sn,
		DeviceId: deviceId,
	}

	// 绑定成功后，移除缓存中的key - 对应 Java: bindKeyCache.removeBindKey(deviceId, tenantId, DeviceKey.DEVICE);
	err = cache.BindKeyCache.RemoveBindKey(ctx, deviceId, tenantId, cache.DeviceKeyDevice)
	if err != nil {
		logger.Warnf("移除缓存bindKey失败: deviceId=%s, error=%v", deviceId, err)
	}

	logger.Infof("二维码绑定成功: userId=%s, deviceId=%s, isNew=%t", userId, deviceId, isNew)
	return webRes.Cb(respEntity)
}

// checkIfVoiceControl 检查是否需要上报语控属性
// 对应 Java: DeviceBindServiceImpl.checkIfVoiceControl()
func (s *deviceBindService) checkIfVoiceControl(ctx context.Context) bool {
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: String voiceControlTenantId = (String) redisTemplate.opsForValue().get(VOICE_TENANT_KEY);
	voiceControlTenantId, err := redis.Rdb.Get(ctx, "VOICE_TENANT_KEY").Result()
	if err != nil {
		logger.Infof("检查是否需要上报属性，获取语控租户ID失败: tenantId=%s, error=%v", tenantId, err)
		return false
	}

	logger.Infof("检查是否需要上报属性，tenantId:%s,voiceTanantId:%s", tenantId, voiceControlTenantId)

	if voiceControlTenantId == "" {
		return false
	}

	// 对应 Java: String[] split = voiceControlTenantId.split(","); for (String s : split) { if (s.equals(tenantId)) return true; }
	tenantIds := strings.Split(voiceControlTenantId, ",")
	for _, id := range tenantIds {
		if id == tenantId {
			return true
		}
	}

	return false
}

// manageBindKeyCache 管理绑定密钥缓存
// 对应 Java: 确认绑定成功后的缓存管理逻辑
func (s *deviceBindService) manageBindKeyCache(ctx context.Context, userId, tenantId, deviceId, bindKey string) {
	// 获取设备配置 - 对应 Java: deviceConfig.getRemove()
	deviceConfigRemove := viper.GetBool("device.bindKey.remove")

	// 对应 Java: if (!deviceConfig.getRemove())
	if !deviceConfigRemove {
		// 对应 Java: Long ttl = Objects.isNull(deviceConfig.getTtl()) || deviceConfig.getTtl() <= 0 ? 120 : deviceConfig.getTtl();
		ttl := int64(120) // 默认120秒
		deviceConfigTtl := viper.GetInt("device.bindKey.ttl")
		if deviceConfigTtl > 0 {
			ttl = int64(deviceConfigTtl)
		}

		// 确认绑定成功后，改变bindKey的过期时间为120秒 - 对应 Java: bindKeyCache.saveBindKeyWithExpire(bk, userId, tenantId, DeviceKey.USER, ttl, TimeUnit.SECONDS);
		bindKeyObj := cache.BindKey{DeviceId: deviceId, BindKey: bindKey}
		err := cache.BindKeyCache.SaveBindKeyWithExpire(ctx, &bindKeyObj, userId, tenantId, cache.DeviceKeyUser,
			time.Duration(ttl)*time.Second)
		if err != nil {
			logger.Warnf("更新绑定密钥缓存过期时间失败: userId=%s, tenantId=%s, ttl=%d, error=%v", userId, tenantId, ttl, err)
		} else {
			logger.Infof("更新绑定密钥缓存过期时间成功: userId=%s, tenantId=%s, ttl=%d", userId, tenantId, ttl)
		}
	} else {
		// 对应 Java: bindKeyCache.removeBindKey(userId, tenantId, DeviceKey.USER);
		err := cache.BindKeyCache.RemoveBindKey(ctx, userId, tenantId, cache.DeviceKeyUser)
		if err != nil {
			logger.Warnf("移除绑定密钥缓存失败: userId=%s, tenantId=%s, error=%v", userId, tenantId, err)
		} else {
			logger.Infof("移除绑定密钥缓存成功: userId=%s, tenantId=%s", userId, tenantId)
		}
	}
}

// BindApp 设备发起绑定APP请求
// 对应 Java: DeviceBindServiceImpl.bindApp(String deviceId, String userId, String key)
func (s *deviceBindService) BindApp(ctx context.Context, req *req.BindAppReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID); String deviceId = SystemContextUtils.getId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	deviceId := ctx.Value(ctxKeys.ID).(string)

	userId := req.UserId
	key := req.Key

	logger.Infof("设备发起绑定APP请求开始: deviceId=%s, userId=%s, key=%s", deviceId, userId, key)

	// 上下文参数验证 - 对应 Java 控制器中的验证
	if tenantId == "" {
		logger.Errorf("租户ID为空: tenantId=%s", tenantId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
	}

	if deviceId == "" {
		logger.Errorf("设备ID为空: deviceId=%s", deviceId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	// 业务参数验证已由 validator 框架在控制器层处理
	// 对应 Java: if (StringUtils.isEmpty(key) || userId == null || StringUtils.isBlank(userId)) { throw new ParameterValidateException("key|userId"); }
	// 这里的验证已经通过 binding:"required" 标签处理

	// 创建绑定密钥对象 - 对应 Java: BindKey bindKey = new BindKey(); bindKey.setDeviceId(deviceId); bindKey.setBindKey(key);
	bindKey := &cache.BindKey{
		DeviceId: deviceId,
		BindKey:  key,
	}

	// 保存绑定密钥到缓存 - 对应 Java: bindKeyCache.saveBindKey(bindKey, userId, tenantId, DeviceKey.USER);
	err := cache.BindKeyCache.SaveBindKey(ctx, bindKey, userId, tenantId, cache.DeviceKeyUser)
	if err != nil {
		logger.Errorf("保存绑定密钥失败: deviceId=%s, userId=%s, error=%v", deviceId, userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "保存绑定密钥失败")
	}

	logger.Infof("设备发起绑定APP请求成功: deviceId=%s, userId=%s, key=%s", deviceId, userId, key)

	// 返回成功响应 - 对应 Java: return ResponseMessage.buildSuccess();
	return webRes.Cbnd()
}

// GetBindKey 获取bindKey
// 对应 Java: DeviceBindServiceImpl.getBindKey(String deviceId, String tenantId)
func (s *deviceBindService) GetBindKey(ctx context.Context, deviceId string) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: String tenantId = SystemContextUtils.getTenantId()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	logger.Infof("获取bindKey开始: deviceId=%s, tenantId=%s", deviceId, tenantId)

	// 1. 参数验证 - 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); }
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId为空")
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
	}

	// 2. 上下文参数验证
	if tenantId == "" {
		logger.Errorf("租户ID为空: tenantId=%s", tenantId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
	}

	// 3. 生成随机绑定密钥 - 对应 Java: String randomBindKey = RandomUtil.getRandomBindKey(BIND_KEY_LENGTH);
	const BIND_KEY_LENGTH = 6 // 对应 Java: BIND_KEY_LENGTH 常量
	randomBindKey := util.GetRandomBindKey(BIND_KEY_LENGTH)

	// 4. 创建绑定密钥对象 - 对应 Java: BindKey bindKey = new BindKey(); bindKey.setBindKey(randomBindKey); bindKey.setDeviceId(deviceId);
	bindKey := &cache.BindKey{
		DeviceId: deviceId,
		BindKey:  randomBindKey,
	}

	// 5. 保存绑定密钥到缓存 - 对应 Java: bindKeyCache.saveBindKey(bindKey, deviceId, tenantId, DeviceKey.DEVICE);
	err := cache.BindKeyCache.SaveBindKey(ctx, bindKey, deviceId, tenantId, cache.DeviceKeyDevice)
	if err != nil {
		logger.Errorf("保存绑定密钥失败: deviceId=%s, tenantId=%s, error=%v", deviceId, tenantId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "保存绑定密钥失败")
	}

	logger.Infof("获取bindKey成功: deviceId=%s, tenantId=%s, bindKey=%s", deviceId, tenantId, randomBindKey)

	// 6. 返回成功响应 - 对应 Java: return randomBindKey;
	return webRes.Cb(randomBindKey)
}
