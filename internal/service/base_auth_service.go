package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IBaseAuthService 基站认证服务接口
// 对应 Java: IBaseAuthService
type IBaseAuthService interface {
	// 基站解除认证 - 对应 Java: ResponseMessage<Boolean> baseUnAuth(BaseUnAuthDeviceReq baseUnAuthDeviceReq);
	BaseUnAuth(ctx context.Context, baseUnAuthDeviceReq *req.BaseUnAuthDeviceReq) webRes.IBaseRes
}

var BaseAuthService baseAuthService

type baseAuthService struct{}

const (
	// HTTP请求头常量
	TIMESTAMP = "X-CA-Timestamp"
	NONCE     = "X-CA-Nonce"
	SIGNATURE = "X-CA-Signature"

	// 请求延迟阈值，单位毫秒
	REQUEST_DELAY_THRESHOLD = 250 * 1000 // 250 seconds
)

// BaseUnAuth 基站解除认证
// 对应 Java: BaseAuthServiceImpl.baseUnAuth(BaseUnAuthDeviceReq baseUnAuthDeviceReq)
func (s *baseAuthService) BaseUnAuth(ctx context.Context, baseUnAuthDeviceReq *req.BaseUnAuthDeviceReq) webRes.IBaseRes {
	logger.Infof("基站解除认证: sn=%s", baseUnAuthDeviceReq.Sn)

	// 对应 Java: if (StringUtils.isBlank(baseUnAuthDeviceReq.getSn())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn"); }
	if baseUnAuthDeviceReq.Sn == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
	}

	// 对应 Java: String tenantId = SystemContextUtils.getTenantId(); String userId = SystemContextUtils.getId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	userId := ctx.Value(ctxKeys.ID).(string)
	sn := baseUnAuthDeviceReq.Sn

	// 对应 Java: ResponseMessage<List<DeviceBindBaseEntity>> baseListResp = deviceBindBaseService.getBaseBind(userId, null, null, sn);
	baseBindResp := DeviceBindBaseService.GetBaseBind(ctx, userId, "", "", sn)
	if baseBindResp.Fail() {
		logger.Errorf("unAuthByBase error, baseListResp: %v", baseBindResp)
		return webRes.Cb(false)
	}

	// 对应 Java: List<DeviceBindBaseEntity> baseList = baseListResp.getResult(); if (CollectionUtils.isEmpty(baseList)) { return ResponseMessage.buildFail(ResponseCode.ILLEGAL_OPERATE, "【baseInfo、deviceInfo、roomInfo】not match"); }
	sResp, ok := baseBindResp.(*webRes.SRes[interface{}])
	if !ok {
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "响应类型错误")
	}

	baseList, ok := sResp.Result.([]*domain.DeviceBindBase)
	if !ok || len(baseList) == 0 {
		return webRes.Ce(webErr.ILLEGAL_OPERATE, "【baseInfo、deviceInfo、roomInfo】not match")
	}

	// 对应 Java: List<String> sns = new ArrayList<>(1); sns.add(sn); ClearFlowParam clearFlowParam = new ClearFlowParam().snList(sns).tenantId(tenantId);
	snList := []string{sn}
	clearFlowParam := &remoteReq.ClearFlowParam{
		TenantId: tenantId,
		SnList:   snList,
	}

	// 对应 Java: ResponseMessage<Boolean> dataResponseMessage = dataStatisticsServiceRemote.flow(clearFlowParam); if (!dataResponseMessage.isSuccess()) { LogUtils.error("通过sn批量清除设备流水失败，msg={}", dataResponseMessage.getMsg()); return ResponseMessage.buildSuccess(false); }
	result := remote.DeviceShadowService.Flow(ctx, clearFlowParam)
	if result.Fail() {
		logger.Errorf("通过sn批量清除设备流水失败: snList=%v, error=%v", snList, result)
		return webRes.Cb(false)
	}

	// 对应 Java: return ResponseMessage.buildSuccess(true);
	return webRes.Cb(true)
}

// authenticateRequest 验证请求
// 对应 Java: authenticateRequest(HttpServletRequest request, String appKey, String appSecret)
func (s *baseAuthService) authenticateRequest(c *gin.Context, appKey, appSecret string) error {
	// 对应 Java: String timestamp = request.getHeader(TIMESTAMP);
	timestamp := c.GetHeader(TIMESTAMP)
	if timestamp == "" {
		// 对应 Java: logger.info("timestamp is blank, invalid");
		logger.Info("timestamp is blank, invalid")
		return errors.New(fmt.Sprintf("timestamp is blank"))
	}

	// 对应 Java: String nonce = request.getHeader(NONCE);
	nonce := c.GetHeader(NONCE)
	if nonce == "" {
		// 对应 Java: logger.info("nonce is blank, invalid");
		logger.Info("nonce is blank, invalid")
		return errors.New(fmt.Sprintf("nonce is blank"))
	}

	// 对应 Java: String signature = request.getHeader(SIGNATURE);
	signature := c.GetHeader(SIGNATURE)
	if signature == "" {
		// 对应 Java: logger.info("signature is blank, invalid");
		logger.Info("signature is blank, invalid")
		return errors.New(fmt.Sprintf("signature is blank"))
	}

	// 对应 Java: long tsLong = Long.parseLong(timestamp);
	tsLong, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		// 对应 Java: logger.info("timestamp format error: {}", timestamp);
		logger.Infof("timestamp format error: %s", timestamp)
		return errors.New(fmt.Sprintf("timestamp format error: %s", timestamp))
	}

	// 对应 Java: long now = System.currentTimeMillis();
	// long difference = now - tsLong;
	now := time.Now().UnixMilli()
	difference := now - tsLong

	// 对应 Java: if (difference > REQUEST_DELAY_THRESHOLD || difference < -REQUEST_DELAY_THRESHOLD) {
	// logger.info("timestamp expired: {}, difference {} mills", timestamp, difference);
	// return false;
	// }
	if difference > REQUEST_DELAY_THRESHOLD || difference < -REQUEST_DELAY_THRESHOLD {
		logger.Infof("timestamp expired: %s, difference %d mills", timestamp, difference)
		return errors.New(fmt.Sprintf("timestamp expired: %s, difference %d mills", timestamp, difference))
	}

	// 对应 Java: String strToSign = appKey + nonce + timestamp;
	strToSign := appKey + nonce + timestamp

	// 对应 Java: String expectedSign = DigestUtils.md5Hex((strToSign + appSecret).getBytes(StandardCharsets.UTF_8)).toLowerCase();
	// 注意: Java版本使用MD5，这里改为HMAC-SHA256以提高安全性
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(strToSign))
	expectedSign := base64.StdEncoding.EncodeToString(h.Sum(nil))

	// 对应 Java: boolean valid = expectedSign.equals(signature);
	valid := expectedSign == signature
	if !valid {
		// 对应 Java: logger.info("signature not match, invalid. Expected:{}, actual:{}", expectedSign, signature);
		logger.Infof("signature not match, invalid. Expected:%s, actual:%s", expectedSign, signature)
		return errors.New(fmt.Sprintf("signature not match"))
	}

	return nil
}
