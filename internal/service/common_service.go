package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/service/remote"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// ICommonService 通用服务接口
// 对应 Java: CommonService (遵循规则46: 严格按照Java编码内容转换)
type ICommonService interface {
	// 检查分享记录 - 对应 Java: checkShareRecord(String recordId)
	CheckShareRecord(ctx context.Context, recordId string) webRes.IBaseRes

	// 设置分享消息无效 - 对应 Java: shareInvalidate(String targetId, String uid, boolean pushMsg)
	ShareInvalidate(ctx context.Context, targetId, uid string, pushMsg bool)
}

var CommonService commonService

type commonService struct{}

// CheckShareRecord 检查分享记录
// 对应 Java: CommonService.checkShareRecord(String recordId) (遵循规则46: 严格按照Java编码内容转换)
func (s *commonService) CheckShareRecord(ctx context.Context, recordId string) webRes.IBaseRes {
	logger.Infof(ctx, "检查分享记录: recordId=%s", recordId)

	// 对应 Java: return infrastructureThirdRemote.checkShareRecord(recordId);
	result := remote.InfrastructureThirdService.CheckShareRecord(ctx, recordId)
	if result.Fail() {
		logger.Errorfm(ctx, "检查分享记录失败: recordId=%s, error=%s", recordId, result.Msg)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "检查分享记录失败")
	}

	// 对应 Java: NoticeShareRecordEntity one = responseMessage.getResult();
	shareRecord := result.Result

	logger.Infof(ctx, "检查分享记录成功: recordId=%s", recordId)
	return webRes.Cb(shareRecord)
}

// ShareInvalidate 设置分享消息无效状态
// 对应 Java: CommonService.shareInvalidate(String targetId, String uid, boolean pushMsg)
func (s *commonService) ShareInvalidate(ctx context.Context, targetId, uid string, pushMsg bool) {
	logger.Infof(ctx, "设置分享消息无效: targetId=%s, uid=%s, pushMsg=%v", targetId, uid, pushMsg)

	// 对应 Java: infrastructureThirdRemote.invalidateMsgByTargetIdAndUid(targetId, uid);
	result := remote.InfrastructureThirdService.InvalidateMsgByTargetIdAndUid(ctx, targetId, uid)
	if result.Fail() {
		logger.Errorfm(ctx, "设置分享消息无效失败: targetId=%s, uid=%s, error=%s", targetId, uid, result.Msg)
		return
	}

	// 对应 Java: if(pushMsg){ infrastructureThirdRemote.pushCancelShareMsg(targetId, uid); }
	if pushMsg {
		response := remote.InfrastructureThirdService.PushCancelShareMsg(ctx, targetId, uid)
		if response.Fail() {
			logger.Errorfm(ctx, "推送取消分享消息失败: targetId=%s, uid=%s, error=%s", targetId, uid, response.Msg)
			return
		}
	}

	logger.Infof(ctx, "设置分享消息无效成功: targetId=%s, uid=%s", targetId, uid)
}
