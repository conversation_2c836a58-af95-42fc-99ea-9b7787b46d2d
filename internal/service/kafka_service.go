package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/viper"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service/remote"
	"piceacorp.com/device-service/pkg/message/kafka"
)

// IKafkaService Kafka服务接口
// 对应 Java: KafkaService
type IKafkaService interface {
	// 生成服务调用消息内容 - 对应 Java: String genServiceInvokeMsg(String deviceId, String tenantId, String content, DevicePushTag pushTag)
	GenServiceInvokeMsg(ctx context.Context, deviceId, tenantId, content string, pushTag string) (string, error)

	// 发送服务调用 - 对应 Java: void senServiceInvoke(String deviceId, String tenantId, String content, DevicePushTag pushTag, String topic)
	SenServiceInvoke(ctx context.Context, deviceId, tenantId, content string, pushTag string, topic string) error

	// 批量发送服务调用 - 对应 Java: void sendBatchServiceInvoke(List<String> deviceIds, String tenantId, String content, DevicePushTag pushTag, String topic)
	SendBatchServiceInvoke(ctx context.Context, deviceIds []string, tenantId, content string, pushTag string, topic string) error

	// 设备注册统计 - 对应 Java: void deviceRegister(DeviceInfoEntity infoEntity)
	DeviceRegister(ctx context.Context, infoEntity *domain.DeviceInfo)
}

var KafkaService kafkaService

type kafkaService struct{}

// GenServiceInvokeMsg 生成服务调用消息内容
// 对应 Java: KafkaServiceImpl.genServiceInvokeMsg(String deviceId, String tenantId, String content, DevicePushTag pushTag)
func (s *kafkaService) GenServiceInvokeMsg(ctx context.Context, deviceId, tenantId, content string, pushTag string) (string, error) {
	// 获取设备信息 - 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
	if err != nil {
		logger.Errorfm(ctx, "获取设备信息失败: deviceId=%s, tenantId=%s, error=%v", deviceId, tenantId, err)
		return "", fmt.Errorf("获取设备信息失败: %v", err)
	}

	// 检查设备是否存在 - 对应 Java: if (Objects.isNull(deviceInfo)) { ... throw new AppRuntimeException(...) }
	if deviceInfo == nil {
		logger.Errorfm(ctx, "genServiceInvokeMsg,设备不存在,deviceId=%s", deviceId)
		return "", fmt.Errorf("设备不存在: deviceId=%s", deviceId)
	}

	// 获取设备属性 - 对应 Java 中获取 sn 和 infoZone
	sn := deviceInfo.Sn
	infoZone := deviceInfo.Zone

	// 构造MQTT方法名 - 对应 Java: String tag = pushTag.getTag(); String mqttMethod = "thing.service." + tag;
	tag := pushTag // 在Go中直接使用传入的字符串
	mqttMethod := "thing.service." + tag

	// 查询设备影子 - 对应 Java: ResponseMessage<DeviceShadowEntity> shadowResp = deviceShadowRemote.findBySN(sn);
	deviceShadow, err := remote.DeviceShadowService.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorfm(ctx, "获取设备影子失败: sn=%s, error=%v", sn, err)
		return "", fmt.Errorf("获取设备影子失败: %v", err)
	}

	// 检查设备影子是否存在 - 对应 Java: if (!shadowResp.isSuccess()) { ... throw new AppRuntimeException(...) }
	if deviceShadow == nil {
		logger.Errorfm(ctx, "获取设备影子失败: sn=%s", sn)
		return "", fmt.Errorf("获取设备影子失败")
	}

	// 获取设备影子属性 - 对应 Java: String version = result.getModelVersion(); String productKey = result.getProductKey();
	version := deviceShadow.ModelVersion
	productKey := deviceShadow.ProductKey

	// 构造MQTT主题 - 对应 Java: String str = "/mqtt/%s/%s/thing/service_invoke/%s"; String mqttTopic = String.format(str, productKey, sn, tag);
	mqttTopic := fmt.Sprintf("/mqtt/%s/%s/thing/service_invoke/%s", productKey, sn, tag)

	// 构造Kafka数据 - 对应 Java: KafkaData kafkaData = new KafkaData(System.currentTimeMillis(), mqttTopic, mqttMethod, sn, tenantId, productKey, version, infoZone, System.currentTimeMillis(), content);
	now := time.Now().UnixMilli()
	kafkaData := vo.KafkaData{
		MessageId:  now,
		Topic:      mqttTopic,
		Method:     mqttMethod,
		Sn:         sn,
		TenantId:   tenantId,
		ProductKey: productKey,
		Version:    version,
		Zone:       infoZone,
		Timestamp:  now,
		Data:       content,
	}

	// 转换为JSON - 对应 Java: return JsonUtils.toJSON(kafkaData);
	kafkaDataJSON, err := json.Marshal(kafkaData)
	if err != nil {
		logger.Errorfm(ctx, "序列化Kafka数据失败: error=%v", err)
		return "", fmt.Errorf("序列化Kafka数据失败: %v", err)
	}

	return string(kafkaDataJSON), nil
}

// SenServiceInvoke 发送服务调用
// 对应 Java: KafkaServiceImpl.senServiceInvoke(String deviceId, String tenantId, String content, DevicePushTag pushTag, String topic)
func (s *kafkaService) SenServiceInvoke(ctx context.Context, deviceId, tenantId, content string, pushTag string, topic string) error {
	// 生成消息 - 对应 Java: String data = genServiceInvokeMsg(deviceId, tenantId, content, pushTag);
	data, err := s.GenServiceInvokeMsg(ctx, deviceId, tenantId, content, pushTag)
	if err != nil {
		return err
	}

	// 发送消息 - 对应 Java: kafkaProducer.sendMessage(topic, data);
	err = kafka.Producer.SendMessage(ctx, topic, data)
	if err != nil {
		logger.Errorfm(ctx, "发送Kafka消息失败: topic=%s, deviceId=%s, error=%v", topic, deviceId, err)
		return err
	}

	logger.Infof(ctx, "发送服务调用成功: deviceId=%s, topic=%s", deviceId, topic)
	return nil
}

// SendBatchServiceInvoke 批量发送服务调用
// 对应 Java: KafkaServiceImpl.sendBatchServiceInvoke(List<String> deviceIds, String tenantId, String content, DevicePushTag pushTag, String topic)
func (s *kafkaService) SendBatchServiceInvoke(ctx context.Context, deviceIds []string, tenantId, content string, pushTag string, topic string) error {
	// 检查设备ID列表是否为空 - 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return; }
	if len(deviceIds) == 0 {
		logger.Warnf(ctx, "设备ID列表为空，跳过批量发送")
		return nil
	}

	logger.Infof(ctx, "批量发送服务调用开始: deviceCount=%d, tenantId=%s, topic=%s", len(deviceIds), tenantId, topic)

	// 遍历发送消息 - 对应 Java: for (String deviceId : deviceIds) { ... }
	for _, deviceId := range deviceIds {
		// 对应 Java: String data = genServiceInvokeMsg(deviceId, tenantId, content, pushTag); kafkaProducer.sendMessage(topic, data);
		err := s.SenServiceInvoke(ctx, deviceId, tenantId, content, pushTag, topic)
		if err != nil {
			logger.Warnf(ctx, "发送设备[%s]消息失败: error=%v", deviceId, err)
			// 不返回错误，继续处理下一个设备
		}
	}

	logger.Infof(ctx, "批量发送服务调用完成: deviceCount=%d, tenantId=%s, topic=%s", len(deviceIds), tenantId, topic)
	return nil
}

// DeviceRegister 设备注册统计
// 对应 Java: KafkaServiceImpl.deviceRegister(DeviceInfoEntity infoEntity)
func (s *kafkaService) DeviceRegister(ctx context.Context, infoEntity *domain.DeviceInfo) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取设备注册主题 - 对应 Java: @Value("${kafka.bigDataService.device.register:deviceRegister}")
		deviceRegisterTopic := viper.GetString("kafka.bigDataService.device.register")
		if deviceRegisterTopic == "" {
			deviceRegisterTopic = "deviceRegister"
		}

		logger.Infof(ctx, "统计激活设备开始: deviceId=%s, sn=%s, topic=%s", infoEntity.Id, infoEntity.Sn, deviceRegisterTopic)

		// 对应 Java: kafkaTemplate.send(deviceRegister, JsonUtils.toJSON(infoEntity));
		deviceJSON, err := json.Marshal(infoEntity)
		if err != nil {
			logger.Errorfm(ctx, "设备统计，序列化设备信息失败: deviceId=%s, error=%v", infoEntity.Id, err)
			return
		}

		// 发送到Kafka
		err = kafka.Producer.SendMessage(ctx, deviceRegisterTopic, string(deviceJSON))
		if err != nil {
			logger.Errorfm(ctx, "设备统计，写入 kafka 异常: deviceId=%s, topic=%s, error=%v", infoEntity.Id, deviceRegisterTopic, err)
			return
		}

		logger.Infof(ctx, "统计激活设备成功: deviceId=%s, sn=%s, topic=%s", infoEntity.Id, infoEntity.Sn, deviceRegisterTopic)
	}()
}
