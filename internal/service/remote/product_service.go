package remote

import (
	"context"
	"fmt"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service/remote/req"
	cres "piceacorp.com/device-service/internal/service/remote/res"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

type productService struct{}

func (s *productService) Name() string {
	return "product-service-go"
}

var ProductService = &productService{}

func (s *productService) GetByCodeAdSn(ctx context.Context, sn, code string, register bool) *res.CSRes[*cres.InnerDeviceLoginCRes] {
	url := fmt.Sprintf("/inner/snDetail/codeAndSn?sn=%s&code=%s&register=%v", sn, code, register)
	return remote.Get[*res.CSRes[*cres.InnerDeviceLoginCRes]](ctx, url, s)
}

func (s *productService) GetSnDetailSellRegin(ctx context.Context, sn string) *res.CSRes[*cres.ProductSnDetailEntity] {
	url := fmt.Sprintf("/inner/snDetail/getSnDetailSellRegin?sn=%s", sn)
	return remote.Get[*res.CSRes[*cres.ProductSnDetailEntity]](ctx, url, s)
}

func (s *productService) DeviceRegister(ctx context.Context, req *req.DeviceRegister) *res.CSRes[*domain.DeviceInfo] {
	url := "/inner/device/register"
	return remote.Post[*res.CSRes[*domain.DeviceInfo]](ctx, url, req, s)
}

func (s *productService) GetByCode(ctx context.Context, tenantId, code string) *res.CSRes[*cres.ProductModeEntity] {
	url := fmt.Sprintf("/inner/productMode/by/codes?tenantId=%s&code=%s", tenantId, code)
	return remote.Get[*res.CSRes[*cres.ProductModeEntity]](ctx, url, s)
}

// GetListByCode 通过产品型号代码列表获取产品型号信息列表
// 对应 Java: @PutMapping("/inner/productMode/by/codes")
// ResponseMessage<List<ProductModeEntity>> getListByCode(@RequestParam String tenantId, @RequestBody List<String> codes);
func (s *productService) GetListByCode(ctx context.Context, tenantId string, codes []string) *res.CSRes[*[]*cres.ProductModeEntity] {
	url := fmt.Sprintf("/inner/productMode/by/codes?tenantId=%s", tenantId)
	return remote.Put[*res.CSRes[*[]*cres.ProductModeEntity]](ctx, url, codes, s)
}

// GetNodeInfoFromDevice 根据设备信息获取节点信息
// 对应 Java: @PostMapping("/inner/rtc/getNodeInfoFromDevice")
// ResponseMessage<RtcDeviceNodeInfoVo> getNodeInfoFromDevice(@RequestBody NodeInfoFromDeviceParam param);
func (s *productService) GetNodeInfoFromDevice(ctx context.Context, param *req.NodeInfoFromDeviceParam) *res.CSRes[*req.RtcDeviceNodeInfoVo] {
	url := "/inner/rtc/getNodeInfoFromDevice"
	return remote.Post[*res.CSRes[*req.RtcDeviceNodeInfoVo]](ctx, url, param, s)
}

// GetListByProductIds 根据产品ID列表获取产品信息列表
// 对应 Java: ResponseMessage<List<ProductInfoEntity>> getListByProductIds(@RequestBody List<String> productIds);
func (s *productService) GetListByProductIds(ctx context.Context, productIds []string) *res.CSRes[*[]*req.ProductInfoEntity] {
	url := "/inner/productInfo/list/ids"
	return remote.Post[*res.CSRes[*[]*req.ProductInfoEntity]](ctx, url, productIds, s)
}

// GetProductClassify 根据产品ID获取产品分类
// 对应 Java: @GetMapping("/inner/product/classify/{productId}")
// ResponseMessage<Object> getProductClassify(@PathVariable String productId); (遵循规则43: 必须全部转换)
func (s *productService) GetProductClassify(ctx context.Context, productId string) *res.CSRes[*interface{}] {
	url := "/inner/productInfo/classify/" + productId
	return remote.Get[*res.CSRes[*interface{}]](ctx, url, s)
}

// GetProdBasicLangConfig 获取产品基础多语言配置
// 对应 Java: @GetMapping("/inner/productBasicLangConfig/get")
// ResponseMessage<ProductBasicLangConfigEntity> getProdBasicLangConfig(@RequestParam("productId") String productId, @RequestParam("lang") String lang);
func (s *productService) GetProdBasicLangConfig(ctx context.Context, productId, lang string) *res.CSRes[*req.ProductBasicLangConfigEntity] {
	url := fmt.Sprintf("/inner/productBasicLangConfig/get?productId=%s&lang=%s", productId, lang)
	return remote.Get[*res.CSRes[*req.ProductBasicLangConfigEntity]](ctx, url, s)
}
