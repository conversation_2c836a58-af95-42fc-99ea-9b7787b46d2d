package remote

import (
	"context"
	"fmt"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// contentService 内容服务远程调用
type contentService struct{}

// SName 返回服务名称
func (s *contentService) Name() string {
	return "content-service-go"
}

// ContentService 内容服务实例
var ContentService = &contentService{}

// DelByDeviceId 清除设备已签署的协议记录
// 对应 Java: @DeleteMapping("/inner/policies/sign/device/{deviceId}")
// ResponseMessage<Boolean> delByDeviceId(@PathVariable String deviceId);
func (s *contentService) DelByDeviceId(ctx context.Context, deviceId string) *res.CSRes[*bool] {
	url := fmt.Sprintf("/inner/policies/sign/device/%s", deviceId)
	return remote.Delete[*res.CSRes[*bool]](ctx, url, nil, s)
}
