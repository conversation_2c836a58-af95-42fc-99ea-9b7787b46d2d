package req

// JPushShareReq 极光推送分享请求
// 对应 Java: JPushShareReq
type JPushShareReq struct {
	// 消息接收者 - 对应 Java: private String to;
	To string `json:"to"`
	// 分享类型 (0-设备分享, 1-家庭分享) - 对应 Java: private Integer type;
	Type int `json:"type"`
	// 分享状态 (0-正常, 1-同意, 2-拒绝) - 对应 Java: private Integer status;
	Status int `json:"status"`
	// 消息标题 - 对应 Java: private String title;
	Title string `json:"title"`
	// 共享家庭时必填家庭名称 - 对应 Java: private String familyName;
	FamilyName string `json:"familyName,omitempty"`
	// 额外的信息 - 对应 Java: List<PushInfo> list;
	List []*PushInfo `json:"list"`
}
