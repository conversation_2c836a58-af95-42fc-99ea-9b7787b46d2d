package req

import "piceacorp.com/device-service/pkg/web/common/time"

// DeviceShadowEntity 设备影子实体
type DeviceShadowEntity struct {
	// 设备SN
	Id string `json:"id"`
	// 产品Key
	ProductKey string `json:"productKey"`
	// 物模型版本
	ModelVersion string `json:"modelVersion"`
	// 设备影子属性内容
	Properties interface{} `json:"properties"`
	// 设备在线状态
	OnlineStatus bool `json:"onlineStatus"`
	// 区域
	Zone string `json:"zone"`
	// 租户ID
	TenantId string `json:"tenantId"`
	// 创建者：设备ID
	CreateBy string `json:"createBy"`
	// 创建时间
	Timestamp time.Ptime `json:"timestamp"`
}
