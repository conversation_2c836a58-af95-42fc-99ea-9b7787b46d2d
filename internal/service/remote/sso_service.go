package remote

import (
	"context"

	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/pkg/remote"
	remoteRes "piceacorp.com/device-service/pkg/remote/res"
)

type ssoService struct{}

// SName 返回服务名称
func (s *ssoService) Name() string {
	return "sso-service-go"
}

var SsoService ssoService

// GetThirdUserByOpenId 根据OpenId获取第三方用户信息
// 对应 Java: SsoRemote.getThirdUserByOpenId(String openId, String tenantId) (遵循规则46: 严格按照Java编码内容转换)
func (s *ssoService) GetThirdUserByOpenId(ctx context.Context, openId, tenantId string) *remoteRes.CSRes[*res.ThirdUserinfoDTO] {
	url := "/open/getThirdUserByOpenId?openId=" + openId + "&tenantId=" + tenantId
	return remote.Get[*remoteRes.CSRes[*res.ThirdUserinfoDTO]](ctx, url, s)
}
