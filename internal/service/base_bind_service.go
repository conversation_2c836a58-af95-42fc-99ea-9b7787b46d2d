package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/repository"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IBaseBindService 基站绑定服务接口
// 对应 Java: IBaseBindService
type IBaseBindService interface {
	// 查询基站绑定信息 - 对应 Java: ResponseMessage<BaseBindVo> baseBind(String baseId);
	BaseBind(ctx context.Context, baseId string) webRes.IBaseRes
}

var BaseBindService baseBindService

type baseBindService struct{}

// BaseBind 查询基站绑定信息
// 对应 Java: BaseBindServiceImpl.baseBind(String baseId)
func (s *baseBindService) BaseBind(ctx context.Context, baseId string) webRes.IBaseRes {
	logger.Infof(ctx, "查询基站绑定信息: baseId=%s", baseId)

	// 对应 Java: String tenantId = SystemContextUtils.getTenantId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: List<BaseBindVo> resultList = this.baseMapper.getBindListByBaseId(baseId,tenantId);
	resultList, err := repository.DeviceBindBaseRepository.GetBindListByBaseId(ctx, baseId, tenantId)
	if err != nil {
		logger.Errorfm(ctx, "查询基站绑定信息失败: baseId=%s, tenantId=%s, error=%v", baseId, tenantId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询基站绑定信息失败")
	}

	// 对应 Java: if (CollectionUtils.isEmpty(resultList)){ return ResponseMessage.buildSuccess(null); }
	if len(resultList) == 0 {
		logger.Infof(ctx, "查询基站绑定信息为空: baseId=%s", baseId)
		var nilResult *vo.BaseBindVo
		return webRes.Cb(nilResult)
	}

	// 对应 Java: BaseBindVo baseBindVo = resultList.get(0);
	baseBindBo := resultList[0]

	// 转换为BaseBindVo
	baseBindVo := &vo.BaseBindVo{
		BaseId:   baseBindBo.BaseId,
		Sn:       baseBindBo.Sn,
		DeviceId: baseBindBo.DeviceId,
		Owner:    baseBindBo.Owner,
		Mac:      baseBindBo.Mac,
		Nickname: baseBindBo.Nickname,
	}

	// 对应 Java: if(StringUtils.isBlank(baseBindVo.getOwner())){ baseBindVo.setOwner(baseId); }
	if baseBindVo.Owner == "" {
		baseBindVo.Owner = baseId
	}

	// 对应 Java: return ResponseMessage.buildSuccess(baseBindVo);
	logger.Infof(ctx, "查询基站绑定信息成功: baseId=%s", baseId)
	return webRes.Cb(baseBindVo)
}
