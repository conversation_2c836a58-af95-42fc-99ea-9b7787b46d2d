package service

import (
	"context"
	"fmt"
	"time"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/util"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
)

// ILogService 日志服务接口
// 对应 Java: LogService
type ILogService interface {
	// 绑定日志
	BindLog(ctx context.Context, logType, userType, deviceId string)
	// 绑定日志列表
	BindLogList(ctx context.Context, bindLogs []*remoteReq.BindLogEntity)
	// 绑定日志列表(domain类型) - 对应 Java: bindLogList(List<BindLogEntity> bindLogEntityList) (遵循规则47: 必须全部转换)
	BindLogListDomain(ctx context.Context, bindLogs []*domain.BindLog)
}

var LogService logService

type logService struct{}

// BindLog 绑定日志
// 对应 Java: LogServiceImpl.bindLog(String type, String operaType, String deviceId)
func (s *logService) BindLog(ctx context.Context, logType, operaType, deviceId string) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取上下文信息 - 对应 Java: SystemContextUtils.getId(), SystemContextUtils.getTenantId()
		userId := ctx.Value(ctxKeys.ID).(string)
		tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

		// 创建绑定日志实体 - 对应 Java: BindLogEntity bindLogEntity = new BindLogEntity(deviceId, SystemContextUtils.getId(), type, operaType, SystemContextUtils.getTenantId())
		// 按照Java构造函数: this.time = System.currentTimeMillis(); this.deviceId = deviceId; this.userId = userId; this.type = type; this.operateType = operateType; this.tenantId = tenantId;
		now := time.Now().UnixMilli()
		bindLogEntity := &remoteReq.BindLogEntity{
			Id:          util.Id(),
			DeviceId:    deviceId,
			UserId:      userId,
			Type:        logType,
			OperateType: operaType,
			Time:        now, // 对应 this.time = System.currentTimeMillis()
			CreateTime:  now,
			TenantId:    tenantId,
		}

		// 远程调用日志服务 - 对应 Java: ResponseMessage<String> responseMessage = logServiceRemote.report(bindLogEntity)
		response := remote.LogService.Report(ctx, bindLogEntity)
		if response.Fail() {
			// 对应 Java: LogUtils.error("插入绑定或解绑日志失败，msg={}", responseMessage.getMsg())
			logger.Errorfm(ctx, "插入绑定或解绑日志失败，msg=%s", response.Msg)
		} else {
			logger.Infof(ctx, "插入绑定或解绑日志成功: deviceId=%s, type=%s, operaType=%s", deviceId, logType, operaType)
		}
	}()
}

// BindLogList 绑定日志列表
// 对应 Java: LogServiceImpl.bindLogList(List<BindLogEntity> bindLogEntity)
func (s *logService) BindLogList(ctx context.Context, bindLogs []*remoteReq.BindLogEntity) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 远程调用日志服务 - 对应 Java: ResponseMessage<Void> responseMessage = logServiceRemote.reportList(bindLogEntity)
		response := remote.LogService.ReportList(ctx, bindLogs)
		if response.Fail() {
			// 对应 Java: LogUtils.error("插入多条绑定或解绑日志失败，msg={}", responseMessage.getMsg())
			logger.Errorfm(ctx, "插入多条绑定或解绑日志失败，msg=%s", response.Msg)
		} else {
			logger.Infof(ctx, "插入多条绑定或解绑日志成功: count=%d", len(bindLogs))
		}
	}()
}

// BindLogListDomain 绑定日志列表(domain类型)
// 对应 Java: LogServiceImpl.bindLogList(List<BindLogEntity> bindLogEntityList) (遵循规则46: 严格按照Java编码内容转换)
func (s *logService) BindLogListDomain(ctx context.Context, bindLogs []*domain.BindLog) {
	// 转换为远程调用所需的类型
	var remoteLogs []*remoteReq.BindLogEntity
	for _, bindLog := range bindLogs {
		remoteLog := &remoteReq.BindLogEntity{
			Id:          bindLog.Id,
			DeviceId:    bindLog.DeviceId,
			UserId:      bindLog.UserId,
			Type:        fmt.Sprintf("%d", bindLog.LogType),   // 转换为字符串
			OperateType: fmt.Sprintf("%d", bindLog.OperateBy), // 转换为字符串
			Time:        bindLog.CreateTime.UnixMilli(),
			CreateTime:  bindLog.CreateTime.UnixMilli(),
			TenantId:    bindLog.TenantId,
		}
		remoteLogs = append(remoteLogs, remoteLog)
	}

	// 调用原有的方法
	s.BindLogList(ctx, remoteLogs)
}
