package service

import (
	"context"

	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/data/cache"
)

// IIcloudIntentService 语控关系服务接口
// 对应 Java: IcloudIntentService
type IIcloudIntentService interface {
	// 单条保存
	Insert(ctx context.Context, userId, sn string)
	// 单条删除
	Remove(ctx context.Context, userId, sn string)
	// 用户列表批量保存
	BatchInsertByUserIds(ctx context.Context, userIds []string, sn string)
	// 用户列表批量删除
	BatchRemoveByUserIds(ctx context.Context, userIds []string, sn string)
	// 设备SN列表批量保存
	BatchInsertBySns(ctx context.Context, userId string, sns []string)
	// 设备SN列表批量删除
	BatchRemoveBySns(ctx context.Context, userId string, sns []string)
}

var IcloudIntentService icloudIntentService

type icloudIntentService struct{}

const IcloudUserKey = "IcloudUserDevice:User::AllUser_null"

// Insert 单条保存
// 对应 Java: IcloudIntentServiceImpl.insert(String userId, String sn)
func (s *icloudIntentService) Insert(ctx context.Context, userId, sn string) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 || !s.contains(userList, userId) {
			logger.Debugf(ctx, "用户不在语控用户列表中或获取失败: userId=%s", userId)
			return
		}

		// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, Arrays.asList(sn), null)
		req := s.getSyncDeviceReportReq(userId, []string{sn}, nil)

		// 同步设备 - 对应 Java: syncDevice(req)
		s.syncDevice(ctx, req)
	}()
}

// Remove 单条删除
// 对应 Java: IcloudIntentServiceImpl.remove(String userId, String sn)
func (s *icloudIntentService) Remove(ctx context.Context, userId, sn string) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 || !s.contains(userList, userId) {
			logger.Debugf(ctx, "用户不在语控用户列表中或获取失败: userId=%s", userId)
			return
		}

		// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, null, Arrays.asList(sn))
		req := s.getSyncDeviceReportReq(userId, nil, []string{sn})

		// 同步设备 - 对应 Java: syncDevice(req)
		s.syncDevice(ctx, req)
	}()
}

// BatchInsertByUserIds 用户列表批量保存
// 对应 Java: IcloudIntentServiceImpl.batchInsertByUserIds(List<String> userIds, String sn)
func (s *icloudIntentService) BatchInsertByUserIds(ctx context.Context, userIds []string, sn string) {
	if len(userIds) == 0 {
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 {
			logger.Debugf(ctx, "获取语控用户列表失败或为空")
			return
		}

		// 遍历用户ID列表 - 对应 Java: userIds.forEach(userId -> {...})
		for _, userId := range userIds {
			if !s.contains(userList, userId) {
				continue
			}

			// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, Arrays.asList(sn), null)
			req := s.getSyncDeviceReportReq(userId, []string{sn}, nil)

			// 同步设备 - 对应 Java: syncDevice(req)
			s.syncDevice(ctx, req)
		}
	}()
}

// BatchRemoveByUserIds 用户列表批量删除
// 对应 Java: IcloudIntentServiceImpl.batchRemoveByUserIds(List<String> userIds, String sn)
func (s *icloudIntentService) BatchRemoveByUserIds(ctx context.Context, userIds []string, sn string) {
	if len(userIds) == 0 {
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 {
			logger.Debugf(ctx, "获取语控用户列表失败或为空")
			return
		}

		// 遍历用户ID列表 - 对应 Java: userIds.forEach(userId -> {...})
		for _, userId := range userIds {
			if !s.contains(userList, userId) {
				continue
			}

			// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, null, Arrays.asList(sn))
			req := s.getSyncDeviceReportReq(userId, nil, []string{sn})

			// 同步设备 - 对应 Java: syncDevice(req)
			s.syncDevice(ctx, req)
		}
	}()
}

// BatchInsertBySns 设备SN列表批量保存
// 对应 Java: IcloudIntentServiceImpl.batchInsertBySns(String userId, List<String> sns)
func (s *icloudIntentService) BatchInsertBySns(ctx context.Context, userId string, sns []string) {
	if len(sns) == 0 {
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 || !s.contains(userList, userId) {
			logger.Debugf(ctx, "用户不在语控用户列表中或获取失败: userId=%s", userId)
			return
		}

		// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, sns, null)
		req := s.getSyncDeviceReportReq(userId, sns, nil)

		// 同步设备 - 对应 Java: syncDevice(req)
		s.syncDevice(ctx, req)
	}()
}

// BatchRemoveBySns 设备SN列表批量删除
// 对应 Java: IcloudIntentServiceImpl.batchRemoveBySns(String userId, List<String> sns)
func (s *icloudIntentService) BatchRemoveBySns(ctx context.Context, userId string, sns []string) {
	if len(sns) == 0 {
		return
	}

	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取语控用户列表 - 对应 Java: getIcloudUser()
		userList, err := s.getIcloudUser(ctx)
		if err != nil || len(userList) == 0 || !s.contains(userList, userId) {
			logger.Debugf(ctx, "用户不在语控用户列表中或获取失败: userId=%s", userId)
			return
		}

		// 构建同步设备请求 - 对应 Java: getSyncDeviceReportReq(userId, null, sns)
		req := s.getSyncDeviceReportReq(userId, nil, sns)

		// 同步设备 - 对应 Java: syncDevice(req)
		s.syncDevice(ctx, req)
	}()
}

// getIcloudUser 获取语控用户列表
// 对应 Java: getIcloudUser()
func (s *icloudIntentService) getIcloudUser(ctx context.Context) ([]string, error) {
	// 从Redis获取语控用户列表 - 对应 Java: redisTemplate.opsForList().range(IcloudUserKey, 0, -1)
	result := cache.Rdb.LRange(ctx, IcloudUserKey, 0, -1)
	if result.Err() != nil {
		logger.Errorfm(ctx, "获取语控用户列表失败: error=%v", result.Err())
		return nil, result.Err()
	}

	userList := result.Val()
	logger.Debugf(ctx, "获取语控用户列表成功: count=%d", len(userList))
	return userList, nil
}

// contains 检查用户是否在列表中
func (s *icloudIntentService) contains(userList []string, userId string) bool {
	for _, user := range userList {
		if user == userId {
			return true
		}
	}
	return false
}

// getSyncDeviceReportReq 构建同步设备请求
// 对应 Java: getSyncDeviceReportReq(String userId, List<String> addSns, List<String> removeSns)
func (s *icloudIntentService) getSyncDeviceReportReq(userId string, addSns, removeSns []string) *remoteReq.SyncDeviceReportReq {
	return &remoteReq.SyncDeviceReportReq{
		UserId:    userId,
		AddSns:    addSns,
		RemoveSns: removeSns,
	}
}

// syncDevice 同步设备
// 对应 Java: syncDevice(SyncDeviceReportReq req)
func (s *icloudIntentService) syncDevice(ctx context.Context, req *remoteReq.SyncDeviceReportReq) {
	// 调用远程服务同步设备 - 对应 Java: icloudIntentServiceRemote.syncDevice(req)
	response := remote.CloudIntentService.SyncDevice(ctx, req)
	if response.Fail() {
		logger.Errorfm(ctx, "同步设备失败: userId=%s, error=%s", req.UserId, response.Msg)
	} else {
		logger.Infof(ctx, "同步设备成功: userId=%s", req.UserId)
	}
}
