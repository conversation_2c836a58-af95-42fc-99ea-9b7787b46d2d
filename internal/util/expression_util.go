package util

import (
	"context"
	"strconv"
	"strings"

	"go.3irobotix.net/aiot/common-log/logger"
)

// ExpressionUtil 表达式解析工具类
// 对应 Java: Spring的ExpressionParser功能
type ExpressionUtil struct{}

// EvaluateExpression 条件表达式解析
// 对应 Java: expression.getValue(context, Boolean.class)
func EvaluateExpression(ctx context.Context, expression string, context map[string]interface{}) (bool, error) {
	// 如果表达式为空，返回true（默认匹配）
	if expression == "" {
		return true, nil
	}

	// 处理逻辑与操作 (&&)
	if strings.Contains(expression, "&&") {
		parts := strings.Split(expression, "&&")
		for _, part := range parts {
			subResult, err := EvaluateExpression(ctx, strings.TrimSpace(part), context)
			if err != nil {
				return false, err
			}
			if !subResult {
				return false, nil // 有一个条件不满足，整个表达式为false
			}
		}
		return true, nil // 所有条件都满足
	}

	// 处理逻辑或操作 (||)
	if strings.Contains(expression, "||") {
		parts := strings.Split(expression, "||")
		for _, part := range parts {
			subResult, err := EvaluateExpression(ctx, strings.TrimSpace(part), context)
			if err != nil {
				return false, nil // 忽略错误，继续检查其他条件
			}
			if subResult {
				return true, nil // 有一个条件满足，整个表达式为true
			}
		}
		return false, nil // 没有条件满足
	}

	// 处理等于比较 (==)
	if strings.Contains(expression, "==") {
		parts := strings.Split(expression, "==")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 检查是否为数字比较
			if intValue, err := strconv.Atoi(valueStr); err == nil {
				// 数字比较
				if contextValue, ok := context[key]; ok {
					// 尝试将上下文值转换为整数
					switch cv := contextValue.(type) {
					case int:
						return cv == intValue, nil
					case float64:
						return int(cv) == intValue, nil
					case string:
						if cvInt, err := strconv.Atoi(cv); err == nil {
							return cvInt == intValue, nil
						}
					}
				}
				return false, nil
			} else {
				// 字符串比较（去掉可能的引号）
				valueStr = strings.Trim(valueStr, "'\"")
				if contextValue, ok := context[key]; ok {
					// 尝试将上下文值转换为字符串
					switch cv := contextValue.(type) {
					case string:
						return cv == valueStr, nil
					case int:
						return strconv.Itoa(cv) == valueStr, nil
					case float64:
						return strconv.FormatFloat(cv, 'f', -1, 64) == valueStr, nil
					}
				}
				return false, nil
			}
		}
	}

	// 处理大于比较 (>)
	if strings.Contains(expression, ">") {
		parts := strings.Split(expression, ">")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 尝试转换为数字进行比较
			value, err := strconv.Atoi(valueStr)
			if err != nil {
				return false, err
			}

			if contextValue, ok := context[key]; ok {
				// 尝试将上下文值转换为整数
				switch cv := contextValue.(type) {
				case int:
					return cv > value, nil
				case float64:
					return int(cv) > value, nil
				case string:
					if cvInt, err := strconv.Atoi(cv); err == nil {
						return cvInt > value, nil
					}
				}
			}
		}
	}

	// 处理小于比较 (<)
	if strings.Contains(expression, "<") {
		parts := strings.Split(expression, "<")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 尝试转换为数字进行比较
			value, err := strconv.Atoi(valueStr)
			if err != nil {
				return false, err
			}

			if contextValue, ok := context[key]; ok {
				// 尝试将上下文值转换为整数
				switch cv := contextValue.(type) {
				case int:
					return cv < value, nil
				case float64:
					return int(cv) < value, nil
				case string:
					if cvInt, err := strconv.Atoi(cv); err == nil {
						return cvInt < value, nil
					}
				}
			}
		}
	}

	// 处理大于等于比较 (>=)
	if strings.Contains(expression, ">=") {
		parts := strings.Split(expression, ">=")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 尝试转换为数字进行比较
			value, err := strconv.Atoi(valueStr)
			if err != nil {
				return false, err
			}

			if contextValue, ok := context[key]; ok {
				// 尝试将上下文值转换为整数
				switch cv := contextValue.(type) {
				case int:
					return cv >= value, nil
				case float64:
					return int(cv) >= value, nil
				case string:
					if cvInt, err := strconv.Atoi(cv); err == nil {
						return cvInt >= value, nil
					}
				}
			}
		}
	}

	// 处理小于等于比较 (<=)
	if strings.Contains(expression, "<=") {
		parts := strings.Split(expression, "<=")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 尝试转换为数字进行比较
			value, err := strconv.Atoi(valueStr)
			if err != nil {
				return false, err
			}

			if contextValue, ok := context[key]; ok {
				// 尝试将上下文值转换为整数
				switch cv := contextValue.(type) {
				case int:
					return cv <= value, nil
				case float64:
					return int(cv) <= value, nil
				case string:
					if cvInt, err := strconv.Atoi(cv); err == nil {
						return cvInt <= value, nil
					}
				}
			}
		}
	}

	// 处理不等于比较 (!=)
	if strings.Contains(expression, "!=") {
		parts := strings.Split(expression, "!=")
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			valueStr := strings.TrimSpace(parts[1])

			// 检查是否为数字比较
			if intValue, err := strconv.Atoi(valueStr); err == nil {
				// 数字比较
				if contextValue, ok := context[key]; ok {
					// 尝试将上下文值转换为整数
					switch cv := contextValue.(type) {
					case int:
						return cv != intValue, nil
					case float64:
						return int(cv) != intValue, nil
					case string:
						if cvInt, err := strconv.Atoi(cv); err == nil {
							return cvInt != intValue, nil
						}
					}
				}
				return true, nil // 键不存在，视为不等
			} else {
				// 字符串比较（去掉可能的引号）
				valueStr = strings.Trim(valueStr, "'\"")
				if contextValue, ok := context[key]; ok {
					// 尝试将上下文值转换为字符串
					switch cv := contextValue.(type) {
					case string:
						return cv != valueStr, nil
					case int:
						return strconv.Itoa(cv) != valueStr, nil
					case float64:
						return strconv.FormatFloat(cv, 'f', -1, 64) != valueStr, nil
					}
				}
				return true, nil // 键不存在，视为不等
			}
		}
	}

	// 无法解析的表达式，记录警告并返回false
	logger.Warnf(ctx, "无法解析的条件表达式: %s", expression)
	return false, nil
}
