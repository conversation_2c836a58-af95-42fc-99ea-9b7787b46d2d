package util

import (
	"encoding/json"
	"go.3irobotix.net/aiot/common-log/logger"
	"regexp"
)

// 检测当前元素是否符合java包名
var elementRe = regexp.MustCompile(`^[a-z,A-Z]+(\.[a-z,A-Z]+)+`)

// 检测json是否为java序列化的数据
var checkJavaCache = regexp.MustCompile(`\[(\s)*"[a-z,A-Z]+(\.[a-z,A-Z]+)+"(\s)*,`)

func UnmarshalJavaJsonCache[T any](jb []byte) *T {
	// 检测是否java缓存
	if checkJavaCache.Match(jb) {
		var javaCache any
		err := json.Unmarshal(jb, &javaCache)
		if err != nil {
			logger.NTErrorfm("unmarshal java cache json  fail, %s", string(jb))
			return nil
		}
		jb, _ = json.Marshal(parseJavaCache(javaCache))
	}
	var res T
	err := json.Unmarshal(jb, &res)
	if err != nil {
		logger.NTErrorfm("unmarshal cache json fail, str: %s", string(jb))
		return nil
	}
	return &res
}

// 将java序列化的json，解析为标准json
func parseJavaCache(v any) any {
	if array, ok := v.([]interface{}); ok {
		if elemStr, ok := array[0].(string); ok && elementRe.MatchString(elemStr) {
			actualValue := array[1]
			if ja, ok := actualValue.([]interface{}); ok {
				resArray := make([]interface{}, 0)
				for _, jaElem := range ja {
					resArray = append(resArray, parseJavaCache(jaElem))
				}
				return resArray
			} else {
				return actualValue
			}
		} else {
			resArray := make([]interface{}, 0)
			for _, jaElem := range array {
				resArray = append(resArray, parseJavaCache(jaElem))
			}
			return resArray
		}

	} else if mapObj, ok := v.(map[string]interface{}); ok {
		for mapk, mapv := range mapObj {
			mapObj[mapk] = parseJavaCache(mapv)
		}
		return mapObj
	}
	return v
}
