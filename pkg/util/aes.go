package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"go.3irobotix.net/aiot/common-log/logger"
	"strings"
)

// AESUtil AES加密工具类
// 对应 Java: AESUtil
type AESUtil struct{}

// Encrypt AES加密
// 对应 Java: AESUtil.Encrypt(String plaintext, String key)
func Encrypt(src, tenantId string) (string, error) {
	if strings.TrimSpace(tenantId) == "" {
		return "", errors.New("tanantId为空null")
	}

	// 获取密钥
	key := md5Key16(tenantId)
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		logger.NTError("AES加密创建Cipher失败", err)
		return "", err
	}

	// 填充数据
	content := []byte(src)
	content = pkcs5Padding(content, block.BlockSize())

	// 加密
	encrypted := make([]byte, len(content))
	ecb := newECBEncrypter(block)
	ecb.CryptBlocks(encrypted, content)

	// 返回Base64编码的加密结果
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// Decrypt AES解密
// 对应 Java: AESUtil.Decrypt(String ciphertext, String key)
func Decrypt(src string, tenantId string) (string, error) {
	if strings.TrimSpace(tenantId) == "" {
		return "", errors.New("tanantId为空null")
	}

	// Base64解码
	encrypted, err := base64.StdEncoding.DecodeString(src)
	if err != nil {
		logger.NTError("Base64解码失败", err)
		return "", err
	}

	// 获取密钥
	key := md5Key16(tenantId)
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		logger.NTError("AES解密创建Cipher失败", err)
		return "", err
	}

	// 解密
	decrypted := make([]byte, len(encrypted))
	ecb := newECBDecrypter(block)
	ecb.CryptBlocks(decrypted, encrypted)

	// 去除填充
	decrypted = pkcs5UnPadding(decrypted)

	// 返回解密结果
	return string(decrypted), nil
}

// md5Key16 获取16位MD5密钥，与Java实现保持一致
func md5Key16(tenantId string) string {
	hash := md5.Sum([]byte(tenantId))
	md5Str := hex.EncodeToString(hash[:])
	// 与Java实现一致，获取8-24位的MD5值（16个字符）
	return md5Str[8:24]
}

// pkcs5Padding PKCS5填充
func pkcs5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// pkcs5UnPadding PKCS5去除填充
func pkcs5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// ECB模式加密器
type ecb struct {
	b         cipher.Block
	blockSize int
}

func newECBEncrypter(b cipher.Block) cipher.BlockMode {
	return &ecbEncrypter{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbEncrypter ecb

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

// ECB模式解密器
func newECBDecrypter(b cipher.Block) cipher.BlockMode {
	return &ecbDecrypter{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbDecrypter ecb

func (x *ecbDecrypter) BlockSize() int { return x.blockSize }

func (x *ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Decrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}
