package mysql

import (
	"context"
	"fmt"

	"go.3irobotix.net/aiot/common-log/logger"
	"gorm.io/gorm"
)

// TxFunc 事务函数类型
type TxFunc func(tx *gorm.DB) error

// WithTransaction 事务包装器
func WithTransaction(ctx context.Context, fn TxFunc) error {
	return WithTransactionDB(ctx, DBH.DB, fn)
}

// WithTransactionDB 使用指定DB的事务包装器
func WithTransactionDB(ctx context.Context, db *gorm.DB, fn TxFunc) error {
	if db == nil {
		return fmt.Errorf("数据库连接为空")
	}

	// 开始事务
	tx := db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %w", tx.Error)
	}

	// 使用defer确保事务结束
	defer func() {
		// 处理panic
		if r := recover(); r != nil {
			logger.Errorfm(ctx, "事务执行失败,%v", r)
			tx.Rollback()
			panic(r) // 重新抛出panic
		}
	}()

	// 执行事务函数
	if err := fn(tx); err != nil {
		// 回滚事务
		logger.Warn(ctx, fmt.Sprintf("事务执行失败，回滚: %v", err))
		if rbErr := tx.Rollback().Error; rbErr != nil {
			logger.Error(ctx, "事务回滚失败", rbErr)
			return fmt.Errorf("事务执行失败: %w, 回滚失败: %v", err, rbErr)
		}
		return err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "事务提交失败", err)
		return fmt.Errorf("事务提交失败: %w", err)
	}

	return nil
}
