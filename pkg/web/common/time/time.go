package time

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type Ptime time.Time

func (t Ptime) MarshalJSON() ([]byte, error) {
	// 转成时间戳并输出为 JSON 数字
	return []byte(fmt.Sprintf("%d", time.Time(t).UnixMilli())), nil
}

func (t *Ptime) UnmarshalJSON(b []byte) error {
	// 先尝试解析普通格式（时间戳或ISO字符串）
	s := strings.Trim(string(b), `"`)
	if ts, err := strconv.ParseInt(s, 10, 64); err == nil {
		*t = Ptime(time.UnixMilli(ts))
		return nil
	}

	if parsedTime, err := time.Parse(time.RFC3339, s); err == nil {
		*t = Ptime(parsedTime)
		return nil
	}

	// 尝试解析数字时间戳（支持秒、毫秒、微秒级时间戳）
	if ts, err := strconv.ParseInt(s, 10, 64); err == nil {
		// 根据数字长度自动判断时间戳精度
		switch {
		case ts > 1e18: // 纳秒级（19位）
			*t = Ptime(time.Unix(0, ts))
		case ts > 1e15: // 微秒级（16位）
			*t = Ptime(time.UnixMicro(ts))
		case ts > 1e12: // 毫秒级（13位）
			*t = Ptime(time.UnixMilli(ts))
		default: // 秒级（10位）
			*t = Ptime(time.Unix(ts, 0))
		}
		return nil
	}

	// 尝试解析ISO格式时间字符串
	if parsedTime, err := time.Parse(time.RFC3339Nano, s); err == nil {
		*t = Ptime(parsedTime)
		return nil
	}

	// 解析 sql.NullString 格式
	var nt nullTimeString
	if err := json.Unmarshal(b, &nt); err == nil && nt.Valid {
		// 尝试解析多种可能的时间格式
		layouts := []string{
			"2006-01-02 15:04:05",
			"2006-01-02T15:04:05Z",
			time.RFC3339,
		}

		for _, layout := range layouts {
			if parsedTime, err := time.Parse(layout, nt.String); err == nil {
				*t = Ptime(parsedTime)
				return nil
			}
		}
		return fmt.Errorf("无法解析的时间格式: %s", nt.String)
	}

	return fmt.Errorf("未知的时间格式: %s", string(b))
}

// Scan 实现 sql.Scanner 接口，用于 GORM 从数据库读取
func (pt *Ptime) Scan(value interface{}) error {
	if value == nil {
		*pt = Ptime(time.Time{})
		return nil
	}
	if t, ok := value.(time.Time); ok {
		*pt = Ptime(t)
		return nil
	}
	return nil
}

// Value 实现 driver.Valuer 接口，用于 GORM 写入数据库
func (pt Ptime) Value() (driver.Value, error) {
	t := time.Time(pt)
	if t.IsZero() {
		return nil, nil
	}
	return t, nil
}

func (t *Ptime) Time() time.Time {
	return time.Time(*t)
}

// Now 创建当前时间的 Ptime
func Now() Ptime {
	return Ptime(time.Now())
}

type nullTimeString struct {
	String string `json:"String"` // 对应 JSON 中的 "String" 字段
	Valid  bool   `json:"Valid"`  // 对应 JSON 中的 "Valid" 字段
}
