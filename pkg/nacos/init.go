package nacos

import (
	"fmt"
	"os"
	"piceacorp.com/device-service/pkg/common/config"
)

// InitServiceDiscovery 初始化服务发现
// 返回服务订阅、服务注册、服务取消注册方法 与异常
func Bootstrap() (func(), func(), error) {
	// handle nacos default data
	// the namespaceId of Nacos.When namespace is public, fill in the blank string here.
	if config.SC.Nacos.Namespace == "" {
		config.SC.Nacos.Namespace = os.Getenv("nacos.namespace")
	}
	if config.SC.Nacos.Namespace == "public" {
		config.SC.Nacos.Namespace = ""
	}
	if config.SC.Nacos.Group == "" {
		ng := os.Getenv("nacos.group")
		if ng == "" {
			config.SC.Nacos.Group = "DEFAULT_GROUP"
		} else {
			config.SC.Nacos.Group = ng
		}
	}
	if config.SC.Nacos.Host == "" {
		nh := os.Getenv("nacos.host")
		if nh == "" {
			config.SC.Nacos.Host = "localhost"
		} else {
			config.SC.Nacos.Host = nh
		}
	}
	if config.SC.Nacos.DataId == "" {
		config.SC.Nacos.DataId = config.SC.Server.Name
	}
	if config.SC.Nacos.CommonDataId == "" {
		config.SC.Nacos.CommonDataId = "aiot_go_common_config"
	}
	// 创建Nacos客户端
	err := initClient()
	if err != nil {
		fmt.Println("init nacos client fail", err)
		return nil, nil, err
	}

	// 初始化Nacos远程配置
	err = initConfig()
	if err != nil {
		fmt.Println("init nacos config fail", err)
		return nil, nil, err
	}

	return func() {
			// 初始化Nacos服务注册与发现
			// 当前服务元数据
			metadata := map[string]string{
				"preserved.register.source": "GO",
			}
			// 注册当前服务
			registerService(config.SC.Server.Name, config.SC.Server.Port, metadata)
		}, func() {
			// 注销服务
			deregisterService(config.SC.Server.Name, config.SC.Server.Port)
		}, nil
}
