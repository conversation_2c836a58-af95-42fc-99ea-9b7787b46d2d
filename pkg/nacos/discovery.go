package nacos

import (
	"fmt"
	"piceacorp.com/device-service/pkg/common/config"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"piceacorp.com/device-service/pkg/logger"
)

// RegisterService 注册服务
func registerService(serviceName string, port uint64, metadata map[string]string) {
	param := vo.RegisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		Weight:      10,
		Enable:      true,
		Healthy:     true,
		Ephemeral:   true, // 临时实例，断开连接后自动注销
		Metadata:    metadata,
		GroupName:   config.SC.Nacos.Group,
	}
	success, err := client.namingClient.RegisterInstance(param)
	if err != nil {
		logger.Error(fmt.Sprintf("service: %s, register fail", serviceName), err)
	}
	logger.Info(fmt.Sprintf("service: %s, register %v，IP=%s, Port=%d", serviceName, success, client.localIP, port))
}

// DeregisterService 注销服务
func deregisterService(serviceName string, port uint64) {

	param := vo.DeregisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
	}

	success, err := client.namingClient.DeregisterInstance(param)
	if err != nil {
		logger.Errorf("deregisterService:: %s fail, error: %w", serviceName, err)

	}
	logger.Infof("deregisterService: %s %v", serviceName, success)
}

// GetService 获取服务信息
func getService(serviceName string) (model.Service, error) {
	param := vo.GetServiceParam{
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
	}

	service, err := client.namingClient.GetService(param)
	if err != nil {
		logger.Errorf("failed to retrieve information for service: %s, %w", serviceName, err)
		return model.Service{}, err
	}
	return service, nil
}

// Subscribe 订阅服务变化
func subscribe(serviceName string, callback func([]model.Instance)) error {
	param := vo.SubscribeParam{
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
		SubscribeCallback: func(services []model.Instance, err error) {
			if err != nil {
				logger.Error("service change callback error", err)
				return
			}
			callback(services)
		},
	}

	err := client.namingClient.Subscribe(&param)
	if err != nil {
		logger.Errorf("subscribe service fail: %s, error: %w", serviceName, err)
	}
	return err
}
