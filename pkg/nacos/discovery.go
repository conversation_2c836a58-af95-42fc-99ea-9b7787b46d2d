package nacos

import (
	"fmt"
	"piceacorp.com/device-service/pkg/common/config"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"go.3irobotix.net/aiot/common-log/logger"
)

// RegisterService 注册服务
func registerService(serviceName string, port uint64, metadata map[string]string) {
	param := vo.RegisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		Weight:      10,
		Enable:      true,
		Healthy:     true,
		Ephemeral:   true, // 临时实例，断开连接后自动注销
		Metadata:    metadata,
		GroupName:   config.SC.Nacos.Group,
	}
	success, err := client.namingClient.RegisterInstance(param)
	if err != nil {
		logger.NTError(fmt.Sprintf("service: %s, register fail", serviceName), err)
	}
	logger.NTInfo(fmt.Sprintf("service: %s, register %v，IP=%s, Port=%d", serviceName, success, client.localIP, port))
}

// DeregisterService 注销服务
func deregisterService(serviceName string, port uint64) {

	param := vo.DeregisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
	}

	success, err := client.namingClient.DeregisterInstance(param)
	if err != nil {
		logger.NTErrorf(err, "deregisterService:: %s fail", serviceName)

	}
	logger.NTInfof("deregisterService: %s %v", serviceName, success)
}

// GetService 获取服务信息
func getService(serviceName string) (model.Service, error) {
	param := vo.GetServiceParam{
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
	}

	service, err := client.namingClient.GetService(param)
	if err != nil {
		logger.NTErrorf(err, "failed to retrieve information for service: %s", serviceName)
		return model.Service{}, err
	}
	return service, nil
}

// Subscribe 订阅服务变化
func subscribe(serviceName string, callback func([]model.Instance)) error {
	param := vo.SubscribeParam{
		ServiceName: serviceName,
		GroupName:   config.SC.Nacos.Group,
		SubscribeCallback: func(services []model.Instance, err error) {
			if err != nil {
				logger.NTError("service change callback error", err)
				return
			}
			callback(services)
		},
	}

	err := client.namingClient.Subscribe(&param)
	if err != nil {
		logger.NTErrorf(err, "subscribe service fail: %s", serviceName)
	}
	return err
}
