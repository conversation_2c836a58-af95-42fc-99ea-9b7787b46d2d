package nacos

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"resty.dev/v3"
)

var (
	httpClient            = resty.New()
	serviceCache          = make(map[string][]serviceEndpoint)
	counterMap            = make(map[string]*atomic.Uint64)
	serviceCacheMutex     sync.RWMutex
	subscribeServiceMutex sync.Mutex
)

// ServiceEndpoint 服务端点
type serviceEndpoint struct {
	IP       string
	Port     uint64
	Metadata map[string]string
	Weight   float64
	Healthy  bool
}

func init() {
	// 初始化http客户端调用远程服务时补充相关请求头
	httpClient.AddRequestMiddleware(func(client *resty.Client, req *resty.Request) error {
		gctx, ok := req.Context().(*gin.Context)
		if ok {
			for k, v := range gctx.Keys {
				vs, ok := v.(string)
				if ok {
					req.SetHeader(k, vs)
				}
			}
		}
		req.SetHeader("pre-application", config.SC.Server.Name)
		return nil
	})
}

// CallService 调用服务
func CallService[T any](ctx context.Context, serviceName, path string, method string, reqBody any, failCall func() (failRes T)) (T, error) {
	response := new(T)
	endpoint, err := getServiceEndpoint(serviceName)
	if err != nil {
		return *response, err
	}
	path = strings.TrimPrefix(path, "/")
	url := fmt.Sprintf("http://%s:%d/%s", endpoint.IP, endpoint.Port, path)
	logger.Infof("start call %s api, path: %s", serviceName, url)
	req := httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		//SetResult(responseJson).
		SetAllowMethodDeletePayload(true).
		SetAllowMethodGetPayload(true)
	if reqBody != nil {
		req.SetBody(reqBody)
	}
	var resp *resty.Response
	switch method {
	case http.MethodGet:
		resp, err = req.Get(url)
	case http.MethodPost:
		resp, err = req.Post(url)
	case http.MethodPut:
		resp, err = req.Put(url)
	case http.MethodDelete:
		resp, err = req.Delete(url)
	default:
		return *response, fmt.Errorf("not allow http method: %s", method)
	}

	if err != nil {
		return *response, fmt.Errorf("call service: %s fail,: %w", serviceName, err)
	}

	if !resp.IsSuccess() {
		logger.Errorf("call service: %s fail, http status: %d, res: %s", serviceName, resp.StatusCode(), resp.String())
		if failCall != nil {
			return failCall(), nil
		}
		return *response, nil
	}
	responseBodyBytes := resp.Bytes()
	logger.Infof("end call %s api, path: %s, res: %v", serviceName, url, string(responseBodyBytes))
	err = json.Unmarshal(responseBodyBytes, response)
	if err != nil {
		return *response, err
	}
	return *response, nil
}

// SubscribeService 订阅服务
func subscribeService(serviceName string) []serviceEndpoint {
	// 初始化计数器
	subscribeServiceMutex.Lock()
	defer subscribeServiceMutex.Unlock()
	serverEndPoints := serviceCache[serviceName]
	if serverEndPoints != nil {
		return serverEndPoints
	}

	// 订阅服务
	err := subscribe(serviceName, func(instances []model.Instance) {
		serviceCacheMutex.Lock()
		defer serviceCacheMutex.Unlock()
		updateServiceCache(serviceName, instances)
	})
	if err != nil {
		return nil
	}
	return serviceCache[serviceName]
}

// 更新服务缓存
func updateServiceCache(serviceName string, instances []model.Instance) {
	endpoints := make([]serviceEndpoint, 0, len(instances))
	if len(instances) > 0 {
		for _, instance := range instances {
			if instance.Healthy {
				endpoints = append(endpoints, serviceEndpoint{
					IP:       instance.Ip,
					Port:     instance.Port,
					Metadata: instance.Metadata,
					Weight:   instance.Weight,
					Healthy:  instance.Healthy,
				})
			}
		}
		logger.Infof("service cache refreshed. name: %s endpotions: %v", serviceName, endpoints)
	} else {
		logger.Warnf("service: %s unavailable", serviceName)
	}
	serviceCache[serviceName] = endpoints
	if _, exists := counterMap[serviceName]; !exists {
		counterMap[serviceName] = &atomic.Uint64{}
	}
}

// GetServiceEndpoint 获取服务端点（轮询方式）
func getServiceEndpoint(serviceName string) (*serviceEndpoint, error) {
	serviceCacheMutex.RLock()
	endpoints, exists := serviceCache[serviceName]
	serviceCacheMutex.RUnlock()

	if !exists {
		// 开始订阅 并且 刷新服务
		endpoints = subscribeService(serviceName)
	}

	if endpoints == nil || len(endpoints) == 0 {
		return nil, fmt.Errorf("service [%s] has no healthy instances", serviceName)
	}
	counter := counterMap[serviceName]
	// 轮询选择实例
	idx := int(counter.Load() % uint64(len(endpoints)))
	counter.Add(1)

	// 重置计数器避免溢出
	if counter.Load() > uint64(len(endpoints)-1) {
		counter.Store(0)
	}

	return &endpoints[idx], nil
}
