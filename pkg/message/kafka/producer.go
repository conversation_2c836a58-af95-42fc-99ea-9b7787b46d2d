package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"go.3irobotix.net/aiot/common-log/logger"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/message"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
)

// kafkaProducer Kafka生产者实现
type kafkaProducer struct {
	asyncProducer sarama.AsyncProducer
	syncProducer  sarama.SyncProducer
}

// 全局生产者实例 - 模拟Redis的全局变量方式
var (
	Producer message.Producer
	once     sync.Once
	mutex    sync.RWMutex
)

// InitProducer 初始化Kafka生产者 - 模拟Redis的初始化方式
func InitProducer() (func(), error) {
	logger.NTInfo("start init kafka producer")
	var err error
	var resFn func()
	once.Do(func() {
		saramaConfig := sarama.NewConfig()
		// 生产者配置
		saramaConfig.Producer.RequiredAcks = sarama.WaitForLocal
		saramaConfig.Producer.Return.Successes = true
		saramaConfig.Producer.Return.Errors = true
		//config.Producer.Retry.Max = c.RetryMax
		//config.Producer.Retry.Backoff = c.RetryBackoff
		//config.Producer.Return.Successes = true
		//config.Producer.Return.Errors = true
		//config.Producer.Timeout = c.Timeout
		//config.Producer.MaxMessageBytes = c.MaxMessageBytes

		// 批处理配置
		//config.Producer.Flush.Frequency = c.FlushFrequency
		//config.Producer.Flush.Messages = c.FlushMessages
		//config.Producer.Flush.Bytes = c.FlushBytes

		// 客户端配置
		saramaConfig.ClientID = config.SC.Server.Name + strconv.Itoa(int(time.Now().UnixMilli()))
		//config.Version = sarama.V2_8_0_0 // 使用Kafka 2.8.0版本
		// 创建异步生产者
		//asyncProducer, createErr := sarama.NewAsyncProducer(config.SC.Kafka.Brokers, saramaConfig)
		//if createErr != nil {
		//	err = fmt.Errorf("创建Kafka异步生产者失败: %w", createErr)
		//	return
		//}
		syncProducer, createErr := sarama.NewSyncProducer(strings.Split(config.SC.Kafka.Brokers, ","), saramaConfig)
		if createErr != nil {
			err = fmt.Errorf("failed to create kafka synchronous producer: %w", createErr)
			return
		}

		p := &kafkaProducer{
			//asyncProducer: asyncProducer,
			syncProducer: syncProducer,
		}

		// 启动错误和成功处理协程
		//go p.handleErrors()
		//go p.handleSuccesses()

		Producer = p
		resFn = func() {
			mutex.Lock()
			defer mutex.Unlock()
			err := p.syncProducer.Close()
			if err != nil {
				logger.NTError("kafka producer connection closed fail", err)
				return
			}
			logger.NTInfo("kafka producer connection closed")
		}
		//检测Kafka连接配置
		sendMsgErr := p.SendMessage(context.Background(), "statusPost", "ping")
		if sendMsgErr != nil {
			err = fmt.Errorf("kafka fails to send a test message %w", sendMsgErr)
			return
		}
		logger.NTInfo("kafka producer init success")
	})
	return resFn, err
}

// SendMessage 发送消息
func (p *kafkaProducer) SendMessage(ctx context.Context, topic string, value interface{}) error {
	mutex.RLock()
	defer mutex.RUnlock()
	// 序列化消息值
	valueBytes, err := p.serializeValue(value)
	if err != nil {
		return fmt.Errorf("serializeValue fail: %w", err)
	}

	// 创建消息
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(valueBytes),
		//Headers: []sarama.RecordHeader{
		//	{
		//		Key:   []byte("timestamp"),
		//		Value: []byte(fmt.Sprintf("%d", time.Now().Unix())),
		//	},
		//},
	}
	//if key != "" {
	//	msg.Key = sarama.StringEncoder(key)
	//}

	partition, offset, err := p.syncProducer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("send message fail: %w", err)
	}
	logger.Infof(ctx, "send kafka msg: topic=%s, partition=%d, offset=%d, value=%s", topic, partition, offset, string(valueBytes))
	return nil
	//同步发送消息
	// 异步发送消息
	//select {
	//case p.asyncProducer.Input() <- msg:
	//	return nil
	//case <-ctx.Done():
	//	return ctx.Err()
	//	//case <-time.After(10 * time.Second):
	//	//	return fmt.Errorf("发送消息超时")
	//}
}

// serializeValue 序列化消息值
func (p *kafkaProducer) serializeValue(value interface{}) ([]byte, error) {
	switch v := value.(type) {
	case string:
		return []byte(v), nil
	case []byte:
		return v, nil
	default:
		return json.Marshal(v)
	}
}

// handleErrors 处理错误
func (p *kafkaProducer) handleErrors() {
	for err := range p.asyncProducer.Errors() {
		if err != nil {
			logger.NTError("kafka producer send msg error  %v", err)
		}
	}
}

// handleSuccesses 处理成功
func (p *kafkaProducer) handleSuccesses() {
	for msg := range p.asyncProducer.Successes() {
		if msg != nil {
			logger.NTInfof("kafka producer send msg success  %v", msg)
		}
	}
}
