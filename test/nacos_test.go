package test

import (
	"context"
	"piceacorp.com/device-service/pkg/nacos"
	"testing"
)

// nacos 示例用法
func TestNacosUsage(t *testing.T) {

	_, _, _ = nacos.Bootstrap()

	cres, resErr := nacos.CallService[string](context.Background(), "sso-service", "/auth/modifyCountry", "POST", map[string]interface{}{"country": "15", "ssoId": "123456"},
		func() (failRes string) {
			return "===fc===="
		})
	t.Log(cres)
	if resErr != nil {
		t.Error(resErr)
	}

	// 注销服务实例
	//success, err := nacos.deregisterService(serviceName, port)
	//if err != nil {
	//	logger.Error("注销服务实例失败", err)
	//} else if success {
	//	logger.Info("服务实例注销成功")
	//}
}
